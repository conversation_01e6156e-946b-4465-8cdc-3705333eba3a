#!/usr/bin/env python3
"""
优化后的邮件处理机器人使用示例
展示如何使用AI处理器建议功能
"""

import json
import sys
import os
from typing import Dict, Any

# 添加项目根目录到sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.core.ai_analyzer import AIAnalyzer, AnalysisResult
from src.core.processor_manager import ProcessorManager
from src.core.config_manager import ConfigManager


def simulate_ai_analysis(email_data: Dict[str, Any]) -> AnalysisResult:
    """
    模拟AI分析结果，展示不同类型邮件的处理器建议
    在实际使用中，这会由真正的AI提供商返回
    """
    subject = email_data.get('subject', '').lower()
    body = email_data.get('body', '').lower()
    attachments = email_data.get('attachments', [])
    
    # 根据邮件内容模拟AI分析
    if any(keyword in subject for keyword in ['紧急', '重要', 'urgent', 'important']):
        # 高优先级邮件
        return AnalysisResult(
            category="工作",
            priority="高",
            sentiment="中性",
            summary="紧急工作邮件需要立即处理",
            action_required=True,
            suggested_actions=["立即回复", "通知相关人员", "安排处理时间"],
            processor_suggestions=[
                {
                    'processor_name': 'notification_processor',
                    'reason': '高优先级邮件需要立即通知',
                    'priority': 1,
                    'enabled': True
                },
                {
                    'processor_name': 'ai_reply_processor',
                    'reason': '需要生成专业回复',
                    'priority': 2,
                    'enabled': True
                },
                {
                    'processor_name': 'log_processor',
                    'reason': '记录重要邮件处理',
                    'priority': 999,
                    'enabled': True
                }
            ],
            confidence=0.9
        )
    
    elif any(att.get('filename', '').endswith('.pdf') for att in attachments):
        # PDF附件邮件
        return AnalysisResult(
            category="技术支持",
            priority="中",
            sentiment="中性",
            summary="包含PDF附件的邮件需要处理",
            action_required=True,
            suggested_actions=["下载PDF", "提取文本", "分析内容", "发送处理结果"],
            processor_suggestions=[
                {
                    'processor_name': 'pdf_processor',
                    'reason': '处理PDF附件并提取内容',
                    'priority': 1,
                    'enabled': True
                },
                {
                    'processor_name': 'ai_reply_processor',
                    'reason': '生成PDF处理结果回复',
                    'priority': 2,
                    'enabled': True
                },
                {
                    'processor_name': 'log_processor',
                    'reason': '记录PDF处理日志',
                    'priority': 999,
                    'enabled': True
                }
            ],
            confidence=0.85
        )
    
    elif any(keyword in subject + body for keyword in ['会议', 'meeting', '邀请', 'invitation']):
        # 会议邀请邮件
        return AnalysisResult(
            category="会议邀请",
            priority="中",
            sentiment="积极",
            summary="会议邀请需要确认",
            action_required=True,
            suggested_actions=["确认参会", "添加到日历", "回复确认"],
            processor_suggestions=[
                {
                    'processor_name': 'notification_processor',
                    'reason': '会议邀请需要及时通知',
                    'priority': 1,
                    'enabled': True
                },
                {
                    'processor_name': 'auto_reply_processor',
                    'reason': '快速确认收到邀请',
                    'priority': 2,
                    'enabled': True
                },
                {
                    'processor_name': 'log_processor',
                    'reason': '记录会议邀请',
                    'priority': 999,
                    'enabled': True
                }
            ],
            confidence=0.8
        )
    
    elif any(keyword in subject + body for keyword in ['spam', '垃圾', '广告', 'promotion']):
        # 垃圾邮件
        return AnalysisResult(
            category="垃圾邮件",
            priority="低",
            sentiment="消极",
            summary="疑似垃圾邮件",
            action_required=False,
            suggested_actions=["标记为垃圾邮件"],
            processor_suggestions=[
                {
                    'processor_name': 'log_processor',
                    'reason': '仅记录垃圾邮件，不做其他处理',
                    'priority': 999,
                    'enabled': True
                }
            ],
            confidence=0.7
        )
    
    else:
        # 普通邮件
        return AnalysisResult(
            category="个人",
            priority="中",
            sentiment="中性",
            summary="普通邮件",
            action_required=False,
            suggested_actions=["阅读邮件", "根据需要回复"],
            processor_suggestions=[
                {
                    'processor_name': 'auto_reply_processor',
                    'reason': '发送确认收到的自动回复',
                    'priority': 1,
                    'enabled': True
                },
                {
                    'processor_name': 'log_processor',
                    'reason': '记录邮件处理',
                    'priority': 999,
                    'enabled': True
                }
            ],
            confidence=0.6
        )


def demo_email_processing():
    """演示邮件处理流程"""
    print("=== 优化后的邮件处理机器人演示 ===\n")
    
    # 模拟不同类型的邮件
    test_emails = [
        {
            'subject': '紧急：服务器故障需要立即处理',
            'sender': '<EMAIL>',
            'body': '生产服务器出现故障，需要立即处理。请尽快联系运维团队。',
            'attachments': [],
            'uid': '001'
        },
        {
            'subject': '项目文档审查',
            'sender': '<EMAIL>',
            'body': '请审查附件中的项目文档，并提供反馈意见。',
            'attachments': [
                {'filename': 'project_doc.pdf', 'content_type': 'application/pdf', 'content': b'fake_pdf_content'}
            ],
            'uid': '002'
        },
        {
            'subject': '下周团队会议邀请',
            'sender': '<EMAIL>',
            'body': '邀请您参加下周一的团队会议，请确认是否能够参加。',
            'attachments': [],
            'uid': '003'
        },
        {
            'subject': '限时优惠！立即购买',
            'sender': '<EMAIL>',
            'body': '特价商品限时优惠，立即购买享受50%折扣！',
            'attachments': [],
            'uid': '004'
        }
    ]
    
    # 模拟处理器管理器（简化版）
    class MockProcessorManager:
        def __init__(self):
            self.processors = {
                'log_processor': type('MockProcessor', (), {'name': 'log_processor', 'enabled': True})(),
                'notification_processor': type('MockProcessor', (), {'name': 'notification_processor', 'enabled': True})(),
                'auto_reply_processor': type('MockProcessor', (), {'name': 'auto_reply_processor', 'enabled': True})(),
                'pdf_processor': type('MockProcessor', (), {'name': 'pdf_processor', 'enabled': True})(),
                'ai_reply_processor': type('MockProcessor', (), {'name': 'ai_reply_processor', 'enabled': True})()
            }
        
        def get_ai_suggested_processors(self, analysis):
            """根据AI建议获取处理器"""
            suggested = []
            for suggestion in analysis.processor_suggestions:
                processor_name = suggestion['processor_name']
                if processor_name in self.processors and suggestion['enabled']:
                    suggested.append((processor_name, suggestion))
            return suggested
    
    processor_manager = MockProcessorManager()
    
    # 处理每封邮件
    for i, email in enumerate(test_emails, 1):
        print(f"--- 处理邮件 {i}: {email['subject']} ---")
        print(f"发件人: {email['sender']}")
        print(f"内容: {email['body'][:50]}...")
        print(f"附件: {len(email['attachments'])}个")
        
        # AI分析
        analysis = simulate_ai_analysis(email)
        print(f"\nAI分析结果:")
        print(f"  分类: {analysis.category}")
        print(f"  优先级: {analysis.priority}")
        print(f"  情感: {analysis.sentiment}")
        print(f"  摘要: {analysis.summary}")
        print(f"  需要行动: {analysis.action_required}")
        print(f"  建议行动: {', '.join(analysis.suggested_actions)}")
        print(f"  置信度: {analysis.confidence}")
        
        # 处理器建议
        print(f"\nAI推荐的处理器:")
        suggested_processors = processor_manager.get_ai_suggested_processors(analysis)
        for processor_name, suggestion in suggested_processors:
            print(f"  ✓ {processor_name}: {suggestion['reason']} (优先级: {suggestion['priority']})")
        
        print(f"\n处理结果: 将按优先级执行 {len(suggested_processors)} 个处理器")
        print("=" * 60 + "\n")


def demo_ai_prompt_optimization():
    """演示AI提示词优化"""
    print("=== AI提示词优化演示 ===\n")
    
    print("优化前的提示词:")
    old_prompt = """你是一个专业的邮件分析助手。请分析邮件内容并返回JSON格式的结果，包含以下字段：
- category: 邮件分类（工作、个人、营销、垃圾邮件等）
- priority: 优先级（高、中、低）
- sentiment: 情感倾向（积极、中性、消极）
- summary: 邮件摘要（50字以内）
- action_required: 是否需要行动（true/false）
- suggested_actions: 建议的行动列表
- confidence: 分析置信度（0-1之间的浮点数）"""
    
    print(old_prompt)
    
    print("\n" + "="*50 + "\n")
    
    print("优化后的提示词:")
    new_prompt = """你是一个专业的邮件分析助手。请分析邮件内容并返回JSON格式的结果，包含以下字段：

- category: 邮件分类（工作、个人、营销、垃圾邮件、技术支持、会议邀请等）
- priority: 优先级（高、中、低）
- sentiment: 情感倾向（积极、中性、消极）
- summary: 邮件摘要（50字以内）
- action_required: 是否需要行动（true/false）
- suggested_actions: 建议的行动列表（具体的行动描述）
- processor_suggestions: 建议使用的处理器列表，每个元素包含：
  * processor_name: 处理器名称（log_processor, notification_processor, auto_reply_processor, pdf_processor, ai_reply_processor）
  * reason: 推荐理由
  * priority: 执行优先级（1-10，数字越小优先级越高）
  * enabled: 是否启用（true/false）
- confidence: 分析置信度（0-1之间的浮点数）

可用的处理器说明：
- log_processor: 记录邮件处理日志（所有邮件都会使用）
- notification_processor: 发送重要邮件通知（高优先级邮件）
- auto_reply_processor: 自动回复邮件（需要快速响应的邮件）
- pdf_processor: 处理PDF附件（包含PDF附件的邮件）
- ai_reply_processor: 使用AI生成智能回复（复杂问题或需要详细回复的邮件）

请根据邮件内容智能推荐合适的处理器组合，确保返回有效的JSON格式。"""
    
    print(new_prompt)
    
    print("\n主要改进:")
    print("✓ 增加了processor_suggestions字段")
    print("✓ 详细说明了每个处理器的功能")
    print("✓ 提供了处理器选择的指导原则")
    print("✓ 要求AI根据邮件内容智能推荐处理器组合")


def main():
    """主函数"""
    print("开始演示优化后的邮件处理机器人...\n")
    
    try:
        # 演示1: 邮件处理流程
        demo_email_processing()
        
        # 演示2: AI提示词优化
        demo_ai_prompt_optimization()
        
        print("\n=== 演示总结 ===")
        print("✅ AI分析器已成功优化，支持处理器建议")
        print("✅ ProcessorManager已增强，支持AI建议的处理器选择")
        print("✅ 系统现在可以根据邮件内容智能选择最合适的处理器组合")
        print("✅ 提高了邮件处理的准确性和效率")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
