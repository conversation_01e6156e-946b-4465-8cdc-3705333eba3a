# 邮件处理机器人环境变量示例
# 复制此文件为 .env 并填入您的实际配置

# 邮箱配置
EMAIL_HOST=imap.gmail.com
EMAIL_PORT=993
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_USE_SSL=true
EMAIL_PROTOCOL=imap

# AI配置
AI_PROVIDER=openai
AI_API_KEY=your-api-key
AI_MODEL=gpt-3.5-turbo
AI_MAX_TOKENS=1000
AI_TEMPERATURE=0.7

# 调度器配置
SCHEDULER_CHECK_INTERVAL=300
SCHEDULER_MAX_EMAILS_PER_CHECK=50
SCHEDULER_ENABLE=true

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/mailer.log

# 安全配置
USE_KEYRING=true
ENCRYPT_CONFIG=false
