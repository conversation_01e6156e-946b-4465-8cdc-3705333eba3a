"""
PDF处理器模块 - 向后兼容接口

此文件保持向后兼容性，重新导出新模块化架构中的所有组件。

原有的导入方式仍然有效：
    from src.processors.pdf_processor import PdfProcessor

新的模块化导入方式（推荐）：
    from src.processors.pdf import PdfProcessor

注意：建议使用新的模块化导入方式以获得更好的代码组织和维护性。
"""

# 从新的模块化架构中导入所有组件
from .pdf import (
    # 主要类
    PdfProcessor,
    PDFMetadataExtractor,
    PDFTextExtractor,
    PDFTextAnalyzer,
    
    # 数据模型
    PDFMetadata,
    PDFTextAnalysis,
    PDFProcessingResult,
    PDFProcessingStatus,
    TextExtractionMethod,
    
    # 异常类
    PDFProcessingError,
    PDFFileTooLargeError,
    PDFCorruptedError,
    PDFEncryptedError,
    PDFTextExtractionError,
    PDFMemoryError,
    
    # 验证器和工具
    validate_pdf_file,
    is_pdf_attachment,
    calculate_file_hash,
    generate_safe_filename,
    format_file_size,
    get_current_time,
    send_email_reply,
    
    # 常量
    MAX_PDF_SIZE,
    SUPPORTED_PDF_MIMES,
    PDF_EXTENSIONS
)

# 为了完全向后兼容，重新导出所有符号
__all__ = [
    # 主要类
    'PdfProcessor',
    'PDFMetadataExtractor',
    'PDFTextExtractor',
    'PDFTextAnalyzer',

    # 数据模型
    'PDFMetadata',
    'PDFTextAnalysis',
    'PDFProcessingResult',
    'PDFProcessingStatus',
    'TextExtractionMethod',

    # 异常类
    'PDFProcessingError',
    'PDFFileTooLargeError',
    'PDFCorruptedError',
    'PDFEncryptedError',
    'PDFTextExtractionError',
    'PDFMemoryError',

    # 验证器和工具
    'validate_pdf_file',
    'is_pdf_attachment',
    'calculate_file_hash',
    'generate_safe_filename',
    'format_file_size',
    'get_current_time',
    'send_email_reply',

    # 常量
    'MAX_PDF_SIZE',
    'SUPPORTED_PDF_MIMES',
    'PDF_EXTENSIONS'
]

# 版本信息
__version__ = '2.0.0'
__description__ = 'PDF处理器模块 - 向后兼容接口'
