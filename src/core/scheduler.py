"""
任务调度模块 - 负责定时执行邮件检查和处理任务
"""

import time
import threading
from typing import Callable, Optional, Dict, Any
from datetime import datetime, timedelta
import schedule
from loguru import logger
from .config_manager import SchedulerConfig


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, config: SchedulerConfig):
        self.config = config
        self.running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        self.task_callback: Optional[Callable] = None
        
    def set_task_callback(self, callback: Callable):
        """设置任务回调函数"""
        self.task_callback = callback
        
    def start(self):
        """启动调度器"""
        if not self.config.enable_scheduler:
            logger.info("调度器已禁用")
            return
            
        if self.running:
            logger.warning("调度器已在运行")
            return
            
        if not self.task_callback:
            logger.error("未设置任务回调函数")
            return
            
        self.running = True
        self.stop_event.clear()
        
        # 设置定时任务
        schedule.every(self.config.check_interval).seconds.do(self._run_task)
        
        # 启动调度线程
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        logger.info(f"任务调度器已启动，检查间隔: {self.config.check_interval}秒")
    
    def stop(self):
        """停止调度器"""
        if not self.running:
            return
            
        self.running = False
        self.stop_event.set()
        
        # 清除所有任务
        schedule.clear()
        
        # 等待线程结束
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
            
        logger.info("任务调度器已停止")
    
    def _scheduler_loop(self):
        """调度器主循环"""
        while self.running and not self.stop_event.is_set():
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                logger.error(f"调度器运行出错: {e}")
                time.sleep(5)  # 出错后等待5秒再继续
    
    def _run_task(self):
        """执行任务"""
        try:
            logger.info("开始执行定时任务")
            start_time = datetime.now()
            
            if self.task_callback:
                result = self.task_callback()
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                logger.info(f"定时任务执行完成，耗时: {duration:.2f}秒")
                
                # 记录任务执行结果
                if isinstance(result, dict):
                    processed_count = result.get('processed_count', 0)
                    error_count = result.get('error_count', 0)
                    logger.info(f"处理邮件: {processed_count}封，错误: {error_count}个")
                    
        except Exception as e:
            logger.error(f"定时任务执行失败: {e}")
    
    def run_once(self):
        """立即执行一次任务"""
        if self.task_callback:
            logger.info("手动执行任务")
            self._run_task()
        else:
            logger.error("未设置任务回调函数")
    
    def get_next_run_time(self) -> Optional[datetime]:
        """获取下次运行时间"""
        if not self.running:
            return None
            
        jobs = schedule.jobs
        if jobs:
            # 过滤掉可能的None值，确保min()函数不会处理包含None的序列
            next_runs = [job.next_run for job in jobs if job.next_run is not None]
            if next_runs:
                return min(next_runs)
        return None
    
    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        next_run = self.get_next_run_time()
        next_run_str = next_run.isoformat() if next_run is not None else None
        
        return {
            "running": self.running,
            "enabled": self.config.enable_scheduler,
            "check_interval": self.config.check_interval,
            "next_run_time": next_run_str,
            "jobs_count": len(schedule.jobs)
        }
    
    def update_config(self, new_config: SchedulerConfig):
        """更新配置"""
        old_running = self.running
        
        # 如果正在运行，先停止
        if self.running:
            self.stop()
            
        # 更新配置
        self.config = new_config
        
        # 如果之前在运行且新配置启用了调度器，重新启动
        if old_running and new_config.enable_scheduler:
            self.start()
            
        logger.info("调度器配置已更新")
    
    def __enter__(self):
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop()
