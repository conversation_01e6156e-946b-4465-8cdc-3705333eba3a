"""
示例处理器 - 展示如何创建自定义处理器
"""

from typing import Dict, Any, Optional
from loguru import logger
from ..core.processor_manager import BaseProcessor
from ..core.ai_analyzer import AnalysisResult


class ExampleProcessor(BaseProcessor):
    """示例处理器 - 演示如何创建自定义邮件处理器"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        
        # 从配置中获取参数
        self.target_category = config.get('target_category', '工作') if config else '工作'
        self.min_confidence = config.get('min_confidence', 0.5) if config else 0.5
        
    def can_process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> bool:
        """
        判断是否可以处理该邮件
        
        Args:
            email_data: 邮件数据
            analysis: AI分析结果
            
        Returns:
            bool: 是否可以处理
        """
        # 示例条件：只处理特定分类且置信度足够高的邮件
        return (analysis.category == self.target_category and 
                analysis.confidence >= self.min_confidence)
    
    def process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """
        处理邮件
        
        Args:
            email_data: 邮件数据
            analysis: AI分析结果
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            subject = email_data.get('subject', '')
            sender = email_data.get('sender', '')
            
            logger.info(f"示例处理器正在处理邮件: {subject}")
            
            # 这里可以添加您的自定义处理逻辑
            # 例如：
            # - 发送通知到特定系统
            # - 保存到数据库
            # - 转发到其他邮箱
            # - 调用外部API
            # - 生成报告
            
            # 示例：根据邮件内容执行不同操作
            actions_taken = []
            
            if analysis.priority == '高':
                actions_taken.append("标记为高优先级")
                # 这里可以添加高优先级邮件的特殊处理逻辑
                
            if analysis.action_required:
                actions_taken.append("添加到待办事项")
                # 这里可以添加到待办事项系统
                
            if '会议' in subject or '会议' in email_data.get('body', ''):
                actions_taken.append("检测到会议相关内容")
                # 这里可以添加日历集成逻辑
                
            # 记录处理信息
            processing_info = {
                "processor_name": self.name,
                "email_subject": subject,
                "email_sender": sender,
                "analysis_category": analysis.category,
                "analysis_priority": analysis.priority,
                "confidence": analysis.confidence,
                "actions_taken": actions_taken,
                "processing_time": self._get_current_time()
            }
            
            logger.info(f"示例处理器完成处理，执行了 {len(actions_taken)} 个操作")
            
            return {
                "success": True,
                "message": f"成功处理邮件: {subject}",
                "actions_count": len(actions_taken),
                "processing_info": processing_info
            }
            
        except Exception as e:
            logger.error(f"示例处理器处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "处理过程中发生错误"
            }
    
    def get_priority(self) -> int:
        """
        获取处理器优先级
        
        Returns:
            int: 优先级数字（越小优先级越高）
        """
        return 30  # 中等优先级
    
    def validate_config(self) -> bool:
        """
        验证配置
        
        Returns:
            bool: 配置是否有效
        """
        # 验证目标分类
        valid_categories = ['工作', '个人', '营销', '垃圾邮件', '通知']
        if self.target_category not in valid_categories:
            logger.error(f"无效的目标分类: {self.target_category}")
            return False
            
        # 验证置信度范围
        if not (0.0 <= self.min_confidence <= 1.0):
            logger.error(f"置信度必须在0-1之间: {self.min_confidence}")
            return False
            
        return True
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def update_config(self, new_config: Dict[str, Any]):
        """
        更新配置
        
        Args:
            new_config: 新的配置字典
        """
        self.config.update(new_config)
        self.target_category = new_config.get('target_category', self.target_category)
        self.min_confidence = new_config.get('min_confidence', self.min_confidence)
        
        logger.info(f"处理器 {self.name} 配置已更新")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取处理器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        # 在实际应用中，这里可以返回处理器的统计信息
        # 例如：处理的邮件数量、成功率、平均处理时间等
        return {
            "processor_name": self.name,
            "target_category": self.target_category,
            "min_confidence": self.min_confidence,
            "enabled": self.enabled,
            "priority": self.get_priority()
        }


# 如果您想创建多个相似的处理器，可以继承ExampleProcessor
class WorkEmailProcessor(ExampleProcessor):
    """工作邮件专用处理器"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        # 设置默认配置
        default_config = {
            'target_category': '工作',
            'min_confidence': 0.7
        }
        if config:
            default_config.update(config)
            
        super().__init__(name, default_config)
    
    def process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """工作邮件的特殊处理逻辑"""
        result = super().process(email_data, analysis)
        
        # 添加工作邮件特有的处理逻辑
        if result.get('success'):
            result['work_specific_action'] = "已添加到工作邮件队列"
            
        return result
