"""
自动回复处理器 - 根据邮件内容自动回复
"""

import os
import sys
from typing import Dict, Any, Optional
from loguru import logger

# 添加项目根目录到sys.path
base_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(base_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.core.processor_manager import BaseProcessor
from src.core.ai_analyzer import AnalysisResult


class AutoReplyProcessor(BaseProcessor):
    """自动回复处理器 - 根据分析结果自动回复邮件"""
    
    def __init__(self, name: str, config:  Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.enabled_categories = config.get('enabled_categories', ['工作']) if config else ['工作']
        self.reply_templates = config.get('reply_templates', {}) if config else {}
        self.auto_reply_enabled = config.get('auto_reply_enabled', False) if config else False
        
        # 默认回复模板
        if not self.reply_templates:
            self.reply_templates = {
                '工作': "感谢您的邮件。我已收到您的消息，会尽快回复。",
                '个人': "谢谢您的邮件，我会及时查看并回复。",
                '营销': "感谢您的信息，如有需要我会联系您。"
            }
    
    def can_process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> bool:
        """判断是否需要自动回复"""
        if not self.auto_reply_enabled:
            return False
            
        # 检查邮件分类是否在启用列表中
        if analysis.category not in self.enabled_categories:
            return False
            
        # 检查是否需要行动
        if not analysis.action_required:
            return False
            
        # 避免回复自动生成的邮件
        subject = email_data.get('subject', '').lower()
        if any(keyword in subject for keyword in ['re:', 'fwd:', 'auto-reply', 'out of office']):
            return False
            
        return True
    
    def process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """生成自动回复"""
        try:
            # 获取回复模板
            reply_template = self.reply_templates.get(
                analysis.category, 
                self.reply_templates.get('工作', "感谢您的邮件，我会尽快回复。")
            )
            
            # 生成回复内容
            reply_content = self._generate_reply_content(email_data, analysis, reply_template)
            
            # 在实际应用中，这里应该调用邮件发送功能
            # 目前只是记录日志
            logger.info(f"准备自动回复邮件: {email_data.get('sender', '')}")
            logger.info(f"回复内容: {reply_content}")
            
            return {
                "reply_generated": True,
                "reply_content": reply_content,
                "recipient": email_data.get('sender', ''),
                "subject": f"Re: {email_data.get('subject', '')}",
                "template_used": analysis.category,
                "note": "实际发送功能需要在生产环境中实现"
            }
            
        except Exception as e:
            logger.error(f"生成自动回复失败: {e}")
            return {
                "reply_generated": False,
                "error": str(e)
            }
    
    def _generate_reply_content(self, email_data: Dict[str, Any], analysis: AnalysisResult, template: str) -> str:
        """生成回复内容"""
        sender_name = self._extract_sender_name(email_data.get('sender', ''))
        
        reply_content = f"""
亲爱的 {sender_name}，

{template}

您的邮件主题：{email_data.get('subject', '')}
邮件分类：{analysis.category}
优先级：{analysis.priority}

此邮件为自动回复，请勿直接回复。

祝好！
邮件处理机器人
        """.strip()
        
        return reply_content
    
    def _extract_sender_name(self, sender: str) -> str:
        """从发件人地址中提取姓名"""
        if '<' in sender and '>' in sender:
            # <AUTHOR> <EMAIL>"
            name_part = sender.split('<')[0].strip()
            if name_part:
                return name_part.strip('"')
        
        # 如果没有姓名，使用邮箱地址的用户名部分
        if '@' in sender:
            return sender.split('@')[0]
            
        return sender
    
    def get_priority(self) -> int:
        """自动回复处理器优先级中等"""
        return 50
    
    def validate_config(self) -> bool:
        """验证配置"""
        if not isinstance(self.enabled_categories, list):
            logger.error("enabled_categories 必须是列表类型")
            return False
            
        if not isinstance(self.reply_templates, dict):
            logger.error("reply_templates 必须是字典类型")
            return False
            
        return True
    
    def add_reply_template(self, category: str, template: str):
        """添加回复模板"""
        self.reply_templates[category] = template
        logger.info(f"已添加回复模板: {category}")
    
    def enable_category(self, category: str):
        """启用某个分类的自动回复"""
        if category not in self.enabled_categories:
            self.enabled_categories.append(category)
            logger.info(f"已启用分类自动回复: {category}")
    
    def disable_category(self, category: str):
        """禁用某个分类的自动回复"""
        if category in self.enabled_categories:
            self.enabled_categories.remove(category)
            logger.info(f"已禁用分类自动回复: {category}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取处理器状态"""
        return {
            "auto_reply_enabled": self.auto_reply_enabled,
            "enabled_categories": self.enabled_categories,
            "available_templates": list(self.reply_templates.keys())
        }
