#!/bin/bash

# 邮件处理机器人启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 检查Python版本
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_message "错误: 未找到 python3" $RED
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    print_message "Python版本: $python_version" $BLUE
    
    if [[ $(echo "$python_version >= 3.8" | bc -l) -eq 0 ]]; then
        print_message "错误: 需要 Python 3.8 或更高版本" $RED
        exit 1
    fi
}

# 检查并创建虚拟环境
setup_venv() {
    if [ ! -d "venv" ]; then
        print_message "创建虚拟环境..." $YELLOW
        python3 -m venv venv
    fi
    
    print_message "激活虚拟环境..." $BLUE
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
}

# 安装依赖
install_dependencies() {
    print_message "安装依赖包..." $YELLOW
    pip install -r requirements.txt
}

# 检查配置文件
check_config() {
    if [ ! -f "config/config.yaml" ]; then
        if [ -f "config/config.example.yaml" ]; then
            print_message "配置文件不存在，复制示例配置..." $YELLOW
            cp config/config.example.yaml config/config.yaml
            print_message "请编辑 config/config.yaml 文件，填入您的配置信息" $RED
            exit 1
        else
            print_message "错误: 配置文件和示例文件都不存在" $RED
            exit 1
        fi
    fi
}

# 创建必要的目录
create_directories() {
    mkdir -p logs
    mkdir -p config
}

# 显示帮助信息
show_help() {
    echo "邮件处理机器人启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start     启动机器人 (默认)"
    echo "  once      运行一次处理"
    echo "  test      测试邮箱连接"
    echo "  status    显示状态信息"
    echo "  install   只安装依赖"
    echo "  help      显示此帮助信息"
    echo ""
}

# 主函数
main() {
    local command=${1:-start}
    
    case $command in
        help)
            show_help
            exit 0
            ;;
        install)
            print_message "开始安装..." $GREEN
            check_python
            setup_venv
            install_dependencies
            create_directories
            print_message "安装完成!" $GREEN
            exit 0
            ;;
        start|once|test|status)
            print_message "邮件处理机器人 - $command" $GREEN
            
            # 检查环境
            check_python
            create_directories
            
            # 如果虚拟环境不存在，创建并安装依赖
            if [ ! -d "venv" ]; then
                setup_venv
                install_dependencies
            else
                source venv/bin/activate
            fi
            
            # 检查配置
            check_config
            
            # 运行程序
            case $command in
                start)
                    print_message "启动邮件处理机器人..." $BLUE
                    python3 main.py
                    ;;
                once)
                    print_message "执行一次性处理..." $BLUE
                    python3 main.py --once
                    ;;
                test)
                    print_message "测试邮箱连接..." $BLUE
                    python3 main.py --test
                    ;;
                status)
                    print_message "显示状态信息..." $BLUE
                    python3 main.py --status
                    ;;
            esac
            ;;
        *)
            print_message "未知命令: $command" $RED
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
