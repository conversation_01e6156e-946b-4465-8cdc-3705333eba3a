"""
PDF文件验证器

提供PDF文件的验证功能，包括文件格式检查、大小限制等。
"""

from loguru import logger
from .exceptions import PDFFileTooLargeError, PDFCorruptedError


# ==================== 常量定义 ====================

# 文件大小限制（字节）
MAX_PDF_SIZE = 50 * 1024 * 1024  # 50MB
MAX_MEMORY_USAGE = 100 * 1024 * 1024  # 100MB

# 支持的PDF MIME类型
SUPPORTED_PDF_MIMES = {
    'application/pdf',
    'application/x-pdf',
    'application/acrobat',
    'applications/vnd.pdf',
    'text/pdf',
    'text/x-pdf'
}

# PDF文件扩展名
PDF_EXTENSIONS = {'.pdf'}


# ==================== 验证函数 ====================

def validate_pdf_file(file_content: bytes, filename: str) -> None:
    """
    验证PDF文件
    
    Args:
        file_content: PDF文件内容
        filename: 文件名
        
    Raises:
        PDFFileTooLargeError: 文件过大
        PDFCorruptedError: 文件损坏或格式不正确
    """
    # 检查文件大小
    if len(file_content) > MAX_PDF_SIZE:
        raise PDFFileTooLargeError(len(file_content), MAX_PDF_SIZE)
    
    # 检查文件头（PDF文件应该以%PDF开头）
    if not file_content.startswith(b'%PDF'):
        raise PDFCorruptedError(f"文件 {filename} 不是有效的PDF文件")
    
    logger.debug(f"PDF文件验证通过: {filename}, 大小: {len(file_content)} bytes")


def is_pdf_attachment(attachment: dict) -> bool:
    """
    检查附件是否为PDF文件
    
    Args:
        attachment: 附件信息字典
        
    Returns:
        bool: 是否为PDF文件
    """
    content_type = attachment.get('content_type', '').lower()
    filename = attachment.get('filename', '').lower()
    
    # 检查MIME类型
    if content_type in SUPPORTED_PDF_MIMES:
        return True
    
    # 检查文件扩展名
    if any(filename.endswith(ext) for ext in PDF_EXTENSIONS):
        return True
    
    # 检查内容类型中是否包含pdf
    if 'pdf' in content_type:
        return True
    
    return False
