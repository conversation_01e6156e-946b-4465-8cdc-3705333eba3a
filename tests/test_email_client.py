"""
邮件客户端测试
"""

import pytest
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.core.config_manager import EmailConfig
from src.core.email_client import EmailClient, EmailMessageWrapper


class TestEmailClient:
    """邮件客户端测试类"""

    def test_email_config_validation(self):
        """测试邮件配置验证"""
        # 有效配置
        valid_config = EmailConfig(
            host="imap.gmail.com",
            port=993,
            username="<EMAIL>",
            password="password",
            protocol="imap",
            use_ssl=True
        )
        assert valid_config.protocol == "imap"

        # 无效协议
        with pytest.raises(ValueError):
            EmailConfig(
                host="imap.gmail.com",
                port=993,
                username="<EMAIL>",
                password="password",
                protocol="invalid",
                use_ssl=True
            )

    def test_email_message_parsing(self):
        """测试邮件消息解析"""
        # 这里需要模拟邮件数据
        # 在实际测试中，您可以使用真实的邮件数据或mock对象
        pass

    def test_email_client_initialization(self):
        """测试邮件客户端初始化"""
        config = EmailConfig(
            host="imap.gmail.com",
            port=993,
            username="<EMAIL>",
            password="xyb_1010210507",
            protocol="imap",
            use_ssl=True
        )

        client = EmailClient(config)
        assert client.config == config
        assert client._connection is None


if __name__ == "__main__":
    pytest.main([__file__])
