"""
PDF文本提取器

负责从PDF文件中提取文本内容，支持多种提取引擎。
"""

from typing import Tuple
from loguru import logger

from .models import TextExtractionMethod
from .exceptions import PDFTextExtractionError, PDFEncryptedError

# PDF处理库导入和可用性检查
try:
    from docling.document_converter import DocumentConverter
    DOCLING_AVAILABLE = True
    logger.debug("docling库已加载，支持文本提取")
except ImportError:
    DOCLING_AVAILABLE = False
    logger.debug("docling库未安装，文本提取功能受限")

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
    logger.debug("PyPDF2库已加载，支持文本提取")
except ImportError:
    PYPDF2_AVAILABLE = False
    logger.debug("PyPDF2库未安装，文本提取功能受限")


class PDFTextExtractor:
    """PDF文本提取器"""
    
    def __init__(self):
        self.extraction_methods = []
        # 优先使用docling，然后是PyPDF2
        if DOCLING_AVAILABLE:
            self.extraction_methods.append(
                (self._extract_with_docling, TextExtractionMethod.DOCLING)
            )
        if PYPDF2_AVAILABLE:
            self.extraction_methods.append(
                (self._extract_with_pypdf2, TextExtractionMethod.PYPDF2)
            )
    
    def extract_text(self, filepath: str) -> Tuple[str, TextExtractionMethod, float]:
        """
        提取PDF文本
        
        Args:
            filepath: PDF文件路径
            
        Returns:
            Tuple[str, TextExtractionMethod, float]: (文本内容, 提取方法, 置信度)
        """
        if not self.extraction_methods:
            logger.error("没有可用的PDF文本提取方法")
            return "", TextExtractionMethod.FALLBACK, 0.0
        
        # 尝试不同的提取方法
        for method, method_type in self.extraction_methods:
            try:
                text = method(filepath)
                if text and len(text.strip()) > 0:
                    confidence = self._calculate_extraction_confidence(text)
                    logger.info(f"使用 {method_type.value} 成功提取文本，置信度: {confidence:.2f}")
                    return text, method_type, confidence
            except Exception as e:
                logger.warning(f"文本提取方法 {method_type.value} 失败: {e}")
                continue
        
        # 所有方法都失败
        logger.error("所有文本提取方法都失败")
        return "", TextExtractionMethod.FALLBACK, 0.0
    
    def _extract_with_docling(self, filepath: str) -> str:
        """
        使用docling提取文本

        Args:
            filepath: PDF文件路径

        Returns:
            str: 提取的文本内容

        Raises:
            PDFTextExtractionError: 提取失败时抛出
        """
        if not DOCLING_AVAILABLE:
            raise PDFTextExtractionError("docling库不可用")

        try:
            # 抑制numpy的RuntimeWarning，特别是"Mean of empty slice"警告
            import warnings
            import numpy as np

            with warnings.catch_warnings():
                # 过滤numpy的RuntimeWarning
                warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
                warnings.filterwarnings('ignore', message='Mean of empty slice')
                warnings.filterwarnings('ignore', message='invalid value encountered in.*')

                converter = DocumentConverter()
                result = converter.convert(filepath)
                text = result.document.export_to_text()

            if not text or len(text.strip()) == 0:
                raise PDFTextExtractionError("docling提取的文本为空")

            logger.debug(f"docling成功提取文本，长度: {len(text)} 字符")
            return text

        except Exception as e:
            raise PDFTextExtractionError(f"docling文本提取失败: {e}")
    
    def _extract_with_pypdf2(self, filepath: str) -> str:
        """
        使用PyPDF2提取文本
        
        Args:
            filepath: PDF文件路径
            
        Returns:
            str: 提取的文本内容
            
        Raises:
            PDFTextExtractionError: 提取失败时抛出
            PDFEncryptedError: PDF文件加密时抛出
        """
        if not PYPDF2_AVAILABLE:
            raise PDFTextExtractionError("PyPDF2库不可用")
        
        try:
            text_parts = []
            with open(filepath, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                
                if reader.is_encrypted:
                    raise PDFEncryptedError("PDF文件已加密，无法提取文本")
                
                for page_num, page in enumerate(reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text_parts.append(page_text)
                    except Exception as e:
                        logger.warning(f"第{page_num + 1}页文本提取失败: {e}")
                        continue
            
            text = '\n'.join(text_parts)
            if not text or len(text.strip()) == 0:
                raise PDFTextExtractionError("PyPDF2提取的文本为空")
            
            return text
            
        except PDFEncryptedError:
            raise
        except Exception as e:
            raise PDFTextExtractionError(f"PyPDF2文本提取失败: {e}")
    
    def _calculate_extraction_confidence(self, text: str) -> float:
        """
        计算文本提取置信度
        
        Args:
            text: 提取的文本内容
            
        Returns:
            float: 置信度（0-1之间）
        """
        if not text:
            return 0.0
        
        confidence = 0.0
        
        # 基于文本长度
        if len(text) > 100:
            confidence += 0.3
        elif len(text) > 50:
            confidence += 0.2
        elif len(text) > 10:
            confidence += 0.1
        
        # 基于字符多样性
        unique_chars = len(set(text.lower()))
        if unique_chars > 50:
            confidence += 0.3
        elif unique_chars > 20:
            confidence += 0.2
        elif unique_chars > 10:
            confidence += 0.1
        
        # 基于单词数量
        words = text.split()
        if len(words) > 50:
            confidence += 0.2
        elif len(words) > 20:
            confidence += 0.1
        
        # 基于可读性（简单检查）
        readable_chars = sum(1 for c in text if c.isalnum() or c.isspace())
        if len(text) > 0:
            readability = readable_chars / len(text)
            confidence += readability * 0.2
        
        return min(confidence, 1.0)
