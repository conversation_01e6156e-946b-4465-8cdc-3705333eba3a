"""
PDF处理警告管理器

专门用于管理PDF处理过程中的各种警告，特别是numpy相关的警告。
"""

import warnings
import functools
from typing import Any, Callable, List, Optional
from loguru import logger


class PDFWarningManager:
    """PDF处理警告管理器"""
    
    # 需要抑制的numpy警告模式
    NUMPY_WARNING_PATTERNS = [
        'Mean of empty slice',
        'invalid value encountered in.*',
        'divide by zero encountered in.*',
        'overflow encountered in.*',
        'underflow encountered in.*'
    ]
    
    # 需要抑制的docling相关警告
    DOCLING_WARNING_PATTERNS = [
        'FutureWarning',
        'DeprecationWarning'
    ]
    
    def __init__(self, suppress_numpy: bool = True, suppress_docling: bool = True):
        """
        初始化警告管理器
        
        Args:
            suppress_numpy: 是否抑制numpy警告
            suppress_docling: 是否抑制docling警告
        """
        self.suppress_numpy = suppress_numpy
        self.suppress_docling = suppress_docling
        self.suppressed_warnings = []
    
    def suppress_warnings(self):
        """抑制指定的警告"""
        if self.suppress_numpy:
            # 抑制numpy RuntimeWarning
            for pattern in self.NUMPY_WARNING_PATTERNS:
                warnings.filterwarnings('ignore', message=pattern, category=RuntimeWarning)
                warnings.filterwarnings('ignore', message=pattern, module='numpy')
                self.suppressed_warnings.append(f"numpy: {pattern}")
        
        if self.suppress_docling:
            # 抑制docling相关警告
            warnings.filterwarnings('ignore', category=FutureWarning, module='docling')
            warnings.filterwarnings('ignore', category=DeprecationWarning, module='docling')
            self.suppressed_warnings.extend(['docling: FutureWarning', 'docling: DeprecationWarning'])
        
        logger.debug(f"已抑制 {len(self.suppressed_warnings)} 类警告")
    
    def restore_warnings(self):
        """恢复警告设置"""
        warnings.resetwarnings()
        logger.debug("警告设置已恢复")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.suppress_warnings()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.restore_warnings()


def suppress_pdf_warnings(func: Callable) -> Callable:
    """
    装饰器：抑制PDF处理过程中的警告
    
    Args:
        func: 要装饰的函数
        
    Returns:
        Callable: 装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        with PDFWarningManager():
            return func(*args, **kwargs)
    return wrapper


def create_warning_context(
    suppress_numpy: bool = True,
    suppress_docling: bool = True,
    custom_patterns: Optional[List[str]] = None
):
    """
    创建自定义警告上下文管理器
    
    Args:
        suppress_numpy: 是否抑制numpy警告
        suppress_docling: 是否抑制docling警告
        custom_patterns: 自定义警告模式列表
        
    Returns:
        PDFWarningManager: 警告管理器实例
    """
    manager = PDFWarningManager(suppress_numpy, suppress_docling)
    
    if custom_patterns:
        for pattern in custom_patterns:
            warnings.filterwarnings('ignore', message=pattern)
            manager.suppressed_warnings.append(f"custom: {pattern}")
    
    return manager


class WarningCollector:
    """警告收集器 - 用于收集和分析警告"""
    
    def __init__(self):
        self.collected_warnings = []
        self.original_showwarning = warnings.showwarning
    
    def custom_showwarning(self, message, category, filename, lineno, file=None, line=None):
        """自定义警告处理函数"""
        from datetime import datetime
        warning_info = {
            'message': str(message),
            'category': category.__name__,
            'filename': filename,
            'lineno': lineno,
            'timestamp': datetime.now().isoformat()
        }
        self.collected_warnings.append(warning_info)
        
        # 记录到日志（调试级别）
        logger.debug(f"收集到警告: {category.__name__}: {message} ({filename}:{lineno})")
    
    def start_collecting(self):
        """开始收集警告"""
        warnings.showwarning = self.custom_showwarning
        logger.debug("开始收集警告")
    
    def stop_collecting(self):
        """停止收集警告"""
        warnings.showwarning = self.original_showwarning
        logger.debug(f"停止收集警告，共收集到 {len(self.collected_warnings)} 个警告")
    
    def get_warnings_summary(self) -> dict:
        """获取警告摘要"""
        if not self.collected_warnings:
            return {'total': 0, 'by_category': {}, 'by_module': {}}
        
        by_category = {}
        by_module = {}
        
        for warning in self.collected_warnings:
            category = warning['category']
            filename = warning['filename']
            
            by_category[category] = by_category.get(category, 0) + 1
            by_module[filename] = by_module.get(filename, 0) + 1
        
        return {
            'total': len(self.collected_warnings),
            'by_category': by_category,
            'by_module': by_module,
            'warnings': self.collected_warnings
        }
    
    def clear_warnings(self):
        """清空收集的警告"""
        self.collected_warnings.clear()
        logger.debug("已清空收集的警告")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start_collecting()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop_collecting()


# 预定义的常用警告管理器
numpy_warning_manager = PDFWarningManager(suppress_numpy=True, suppress_docling=False)
docling_warning_manager = PDFWarningManager(suppress_numpy=False, suppress_docling=True)
full_warning_manager = PDFWarningManager(suppress_numpy=True, suppress_docling=True)
