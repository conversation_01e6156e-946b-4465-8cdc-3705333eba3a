"""
AI分析模块 - 负责使用大语言模型分析邮件内容
"""

import json
from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod
import openai
import anthropic
from loguru import logger
from .config_manager import AIConfig


class AnalysisResult:
    """分析结果类"""

    def __init__(self,
                 category: str,
                 priority: str,
                 sentiment: str,
                 summary: str,
                 action_required: bool,
                 suggested_actions: List[str],
                 processor_suggestions: Optional[List[Dict[str, Any]]] = None,
                 confidence: float = 0.0,
                 raw_response: str = ""):
        self.category = category
        self.priority = priority
        self.sentiment = sentiment
        self.summary = summary
        self.action_required = action_required
        self.suggested_actions = suggested_actions
        self.processor_suggestions = processor_suggestions or []
        self.confidence = confidence
        self.raw_response = raw_response

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "category": self.category,
            "priority": self.priority,
            "sentiment": self.sentiment,
            "summary": self.summary,
            "action_required": self.action_required,
            "suggested_actions": self.suggested_actions,
            "processor_suggestions": self.processor_suggestions,
            "confidence": self.confidence
        }


class BaseAIProvider(ABC):
    """AI提供商基类"""
    
    def __init__(self, config: AIConfig):
        self.config = config
    
    @abstractmethod
    def analyze_email(self, email_data: Dict[str, Any]) -> AnalysisResult:
        """分析邮件内容"""
        pass


class OpenAIProvider(BaseAIProvider):
    """OpenAI提供商"""
    
    def __init__(self, config: AIConfig):
        super().__init__(config)
        self.client = openai.OpenAI(
            api_key=config.api_key,
            base_url=config.base_url
        )
    
    def analyze_email(self, email_data: Dict[str, Any]) -> AnalysisResult:
        """使用OpenAI分析邮件"""
        try:
            prompt = self._build_analysis_prompt(email_data)
            
            response = self.client.chat.completions.create(
                model=self.config.model,
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature
            )
            
            result_text = response.choices[0].message.content
            if result_text is None:
                logger.error("OpenAI返回了空内容")
                return self._create_fallback_result()
            return self._parse_analysis_result(result_text)
            
        except Exception as e:
            logger.error(f"OpenAI分析失败: {e}")
            return self._create_fallback_result()
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个专业的邮件分析助手。请分析邮件内容并返回JSON格式的结果，包含以下字段：

        - category: 邮件分类（工作、个人、营销、垃圾邮件、技术支持、会议邀请等）
        - priority: 优先级（高、中、低）
        - sentiment: 情感倾向（积极、中性、消极）
        - summary: 邮件摘要（50字以内）
        - action_required: 是否需要行动（true/false）
        - suggested_actions: 建议的行动列表（具体的行动描述）
        - processor_suggestions: 建议使用的处理器列表，每个元素包含：
          * processor_name: 处理器名称（log_processor, notification_processor, auto_reply_processor, pdf_processor, ai_reply_processor）
          * reason: 推荐理由
          * priority: 执行优先级（1-10，数字越小优先级越高）
          * enabled: 是否启用（true/false）
        - confidence: 分析置信度（0-1之间的浮点数）

        可用的处理器说明：
        - log_processor: 记录邮件处理日志（所有邮件都会使用）
        - notification_processor: 发送重要邮件通知（高优先级邮件）
        - auto_reply_processor: 自动回复邮件（需要快速响应的邮件）
        - pdf_processor: 处理PDF附件（包含PDF附件的邮件）
        - ai_reply_processor: 使用AI生成智能回复（复杂问题或需要详细回复的邮件）

        请根据邮件内容智能推荐合适的处理器组合，确保返回有效的JSON格式。"""
    
    def _build_analysis_prompt(self, email_data: Dict[str, Any]) -> str:
        """构建分析提示词"""
        return f"""
        请分析以下邮件：
        
        主题: {email_data.get('subject', '')}
        发件人: {email_data.get('sender', '')}
        正文: {email_data.get('body', '')[:1000]}  # 限制长度
        附件: {len(email_data.get('attachments', []))}个
        
        请返回JSON格式的分析结果。
        """
    
    def _parse_analysis_result(self, result_text: str) -> AnalysisResult:
        """解析分析结果"""
        try:
            # 尝试提取JSON部分
            start = result_text.find('{')
            end = result_text.rfind('}') + 1
            if start != -1 and end != 0:
                json_text = result_text[start:end]
                data = json.loads(json_text)
            else:
                data = json.loads(result_text)

            # 解析处理器建议
            processor_suggestions = data.get('processor_suggestions', [])
            if not isinstance(processor_suggestions, list):
                processor_suggestions = []

            # 验证处理器建议格式
            validated_suggestions = []
            for suggestion in processor_suggestions:
                if isinstance(suggestion, dict) and 'processor_name' in suggestion:
                    validated_suggestion = {
                        'processor_name': suggestion.get('processor_name', ''),
                        'reason': suggestion.get('reason', ''),
                        'priority': suggestion.get('priority', 50),
                        'enabled': suggestion.get('enabled', True)
                    }
                    validated_suggestions.append(validated_suggestion)

            return AnalysisResult(
                category=data.get('category', '未知'),
                priority=data.get('priority', '中'),
                sentiment=data.get('sentiment', '中性'),
                summary=data.get('summary', ''),
                action_required=data.get('action_required', False),
                suggested_actions=data.get('suggested_actions', []),
                processor_suggestions=validated_suggestions,
                confidence=data.get('confidence', 0.5),
                raw_response=result_text
            )

        except Exception as e:
            logger.error(f"解析分析结果失败: {e}")
            return self._create_fallback_result()
    
    def _create_fallback_result(self) -> AnalysisResult:
        """创建备用分析结果"""
        return AnalysisResult(
            category="未知",
            priority="中",
            sentiment="中性",
            summary="分析失败",
            action_required=False,
            suggested_actions=[],
            processor_suggestions=[
                {
                    'processor_name': 'log_processor',
                    'reason': '默认记录日志',
                    'priority': 999,
                    'enabled': True
                }
            ],
            confidence=0.0
        )


class AnthropicProvider(BaseAIProvider):
    """Anthropic提供商"""
    
    def __init__(self, config: AIConfig):
        super().__init__(config)
        self.client = anthropic.Anthropic(api_key=config.api_key)
    
    def analyze_email(self, email_data: Dict[str, Any]) -> AnalysisResult:
        """使用Anthropic分析邮件"""
        try:
            prompt = self._build_analysis_prompt(email_data)
            
            response = self.client.messages.create(
                model=self.config.model,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            # 尝试从Anthropic响应中提取文本内容
            try:
                # 根据最新的Anthropic API，获取响应文本
                if hasattr(response, 'content'):
                    if isinstance(response.content, list) and len(response.content) > 0:
                        # 尝试获取内容，避免直接访问可能不存在的属性
                        content_item = response.content[0]
                        # 使用更安全的方式获取文本内容
                        result_text = getattr(content_item, 'text', None)
                        if result_text is None:
                            result_text = str(content_item)
                    else:
                        result_text = str(response.content)
                else:
                    # 尝试直接获取响应文本
                    result_text = str(response)
                    
                if not result_text or result_text is None:
                    logger.error("Anthropic返回了空内容")
                    return self._create_fallback_result()
            except Exception as e:
                logger.error(f"解析Anthropic响应失败: {e}")
                return self._create_fallback_result()
            return self._parse_analysis_result(result_text)
            
        except Exception as e:
            logger.error(f"Anthropic分析失败: {e}")
            return self._create_fallback_result()
    
    def _build_analysis_prompt(self, email_data: Dict[str, Any]) -> str:
        """构建分析提示词"""
        return f"""
        你是一个专业的邮件分析助手。请分析以下邮件内容并返回JSON格式的结果：

        邮件信息：
        主题: {email_data.get('subject', '')}
        发件人: {email_data.get('sender', '')}
        正文: {email_data.get('body', '')[:1000]}
        附件数量: {len(email_data.get('attachments', []))}

        请返回包含以下字段的JSON：
        - category: 邮件分类（工作、个人、营销、垃圾邮件、技术支持、会议邀请等）
        - priority: 优先级（高/中/低）
        - sentiment: 情感倾向（积极、中性、消极）
        - summary: 邮件摘要（50字以内）
        - action_required: 是否需要行动（true/false）
        - suggested_actions: 建议的行动列表（具体的行动描述）
        - processor_suggestions: 建议使用的处理器列表，每个元素包含：
          * processor_name: 处理器名称（log_processor, notification_processor, auto_reply_processor, pdf_processor, ai_reply_processor）
          * reason: 推荐理由
          * priority: 执行优先级（1-10，数字越小优先级越高）
          * enabled: 是否启用（true/false）
        - confidence: 分析置信度（0-1之间的浮点数）

        可用的处理器说明：
        - log_processor: 记录邮件处理日志（所有邮件都会使用）
        - notification_processor: 发送重要邮件通知（高优先级邮件）
        - auto_reply_processor: 自动回复邮件（需要快速响应的邮件）
        - pdf_processor: 处理PDF附件（包含PDF附件的邮件）
        - ai_reply_processor: 使用AI生成智能回复（复杂问题或需要详细回复的邮件）

        请根据邮件内容智能推荐合适的处理器组合。
        """
    
    def _parse_analysis_result(self, result_text: str) -> AnalysisResult:
        """解析分析结果"""
        try:
            # 尝试提取JSON部分
            start = result_text.find('{')
            end = result_text.rfind('}') + 1
            if start != -1 and end != 0:
                json_text = result_text[start:end]
                data = json.loads(json_text)
            else:
                data = json.loads(result_text)

            # 解析处理器建议
            processor_suggestions = data.get('processor_suggestions', [])
            if not isinstance(processor_suggestions, list):
                processor_suggestions = []

            # 验证处理器建议格式
            validated_suggestions = []
            for suggestion in processor_suggestions:
                if isinstance(suggestion, dict) and 'processor_name' in suggestion:
                    validated_suggestion = {
                        'processor_name': suggestion.get('processor_name', ''),
                        'reason': suggestion.get('reason', ''),
                        'priority': suggestion.get('priority', 50),
                        'enabled': suggestion.get('enabled', True)
                    }
                    validated_suggestions.append(validated_suggestion)

            return AnalysisResult(
                category=data.get('category', '未知'),
                priority=data.get('priority', '中'),
                sentiment=data.get('sentiment', '中性'),
                summary=data.get('summary', ''),
                action_required=data.get('action_required', False),
                suggested_actions=data.get('suggested_actions', []),
                processor_suggestions=validated_suggestions,
                confidence=data.get('confidence', 0.5),
                raw_response=result_text
            )

        except Exception as e:
            logger.error(f"解析分析结果失败: {e}")
            return self._create_fallback_result()
    
    def _create_fallback_result(self) -> AnalysisResult:
        """创建备用分析结果"""
        return AnalysisResult(
            category="未知",
            priority="中",
            sentiment="中性",
            summary="分析失败",
            action_required=False,
            suggested_actions=[],
            processor_suggestions=[
                {
                    'processor_name': 'log_processor',
                    'reason': '默认记录日志',
                    'priority': 999,
                    'enabled': True
                }
            ],
            confidence=0.0
        )


class AIAnalyzer:
    """AI分析器主类"""
    
    def __init__(self, config: AIConfig):
        self.config = config
        self.provider = self._create_provider()
    
    def _create_provider(self) -> BaseAIProvider:
        """创建AI提供商实例"""
        if self.config.provider.lower() == "openai":
            return OpenAIProvider(self.config)
        elif self.config.provider.lower() == "anthropic":
            return AnthropicProvider(self.config)
        else:
            raise ValueError(f"不支持的AI提供商: {self.config.provider}")
    
    def analyze_email(self, email_data: Dict[str, Any]) -> AnalysisResult:
        """分析邮件内容"""
        logger.info(f"开始分析邮件: {email_data.get('subject', '无主题')}")
        
        result = self.provider.analyze_email(email_data)
        
        logger.info(f"邮件分析完成 - 分类: {result.category}, 优先级: {result.priority}")
        return result
    
    def analyze_text(self, text: str) -> AnalysisResult:
        """分析任意文本内容（将其视为邮件正文）"""
        logger.info(f"开始分析文本内容: {text[:50]}...")
        
        # 将文本包装为邮件数据结构
        email_data = {
            "subject": "文本分析",
            "body": text,
            "sender": "unknown",
            "attachments": []
        }
        
        return self.analyze_email(email_data)
    
    def batch_analyze(self, emails: List[Dict[str, Any]]) -> List[AnalysisResult]:
        """批量分析邮件"""
        results = []
        for email_data in emails:
            result = self.analyze_email(email_data)
            results.append(result)
        return results
