"""
安全工具类 - 提供密码加密、解密等安全功能
"""

import os
import base64
import hashlib
from typing import Optional
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import keyring
from loguru import logger


class SecurityUtils:
    """安全工具类"""
    
    @staticmethod
    def generate_key() -> bytes:
        """生成加密密钥"""
        return Fernet.generate_key()
    
    @staticmethod
    def derive_key_from_password(password: str, salt: Optional[bytes] = None) -> bytes:
        """从密码派生加密密钥"""
        if salt is None:
            salt = os.urandom(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key
    
    @staticmethod
    def encrypt_text(text: str, key: bytes) -> str:
        """加密文本"""
        try:
            f = Fernet(key)
            encrypted_data = f.encrypt(text.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"文本加密失败: {e}")
            raise
    
    @staticmethod
    def decrypt_text(encrypted_text: str, key: bytes) -> str:
        """解密文本"""
        try:
            f = Fernet(key)
            encrypted_data = base64.urlsafe_b64decode(encrypted_text.encode())
            decrypted_data = f.decrypt(encrypted_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"文本解密失败: {e}")
            raise
    
    @staticmethod
    def hash_password(password: str, salt: Optional[str] = None) -> tuple:
        """哈希密码"""
        if salt is None:
            salt = os.urandom(32).hex()
        
        # 使用PBKDF2进行密码哈希
        pwdhash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000
        )
        
        return pwdhash.hex(), salt
    
    @staticmethod
    def verify_password(password: str, hashed_password: str, salt: str) -> bool:
        """验证密码"""
        pwdhash, _ = SecurityUtils.hash_password(password, salt)
        return pwdhash == hashed_password
    
    @staticmethod
    def store_password_securely(service: str, username: str, password: str) -> bool:
        """安全存储密码到系统密钥环"""
        try:
            keyring.set_password(service, username, password)
            logger.info(f"密码已安全存储: {service}:{username}")
            return True
        except Exception as e:
            logger.error(f"密码存储失败: {e}")
            return False
    
    @staticmethod
    def get_password_securely(service: str, username: str) -> Optional[str]:
        """从系统密钥环获取密码"""
        try:
            password = keyring.get_password(service, username)
            if password:
                logger.info(f"密码已从密钥环获取: {service}:{username}")
            return password
        except Exception as e:
            logger.error(f"密码获取失败: {e}")
            return None
    
    @staticmethod
    def delete_password_securely(service: str, username: str) -> bool:
        """从系统密钥环删除密码"""
        try:
            keyring.delete_password(service, username)
            logger.info(f"密码已从密钥环删除: {service}:{username}")
            return True
        except Exception as e:
            logger.error(f"密码删除失败: {e}")
            return False
    
    @staticmethod
    def mask_email(email: str) -> str:
        """掩码邮件地址"""
        if '@' not in email:
            return email
        
        local, domain = email.split('@', 1)
        
        if len(local) <= 2:
            masked_local = '*' * len(local)
        else:
            masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
        
        return f"{masked_local}@{domain}"
    
    @staticmethod
    def mask_password(password: str) -> str:
        """掩码密码"""
        if len(password) <= 2:
            return '*' * len(password)
        return password[0] + '*' * (len(password) - 2) + password[-1]
    
    @staticmethod
    def generate_random_string(length: int = 16) -> str:
        """生成随机字符串"""
        return base64.urlsafe_b64encode(os.urandom(length))[:length].decode()
    
    @staticmethod
    def is_secure_connection(host: str, port: int) -> bool:
        """检查是否为安全连接端口"""
        secure_ports = {
            993,  # IMAPS
            995,  # POP3S
            465,  # SMTPS
            587,  # SMTP with STARTTLS
        }
        return port in secure_ports
    
    @staticmethod
    def validate_ssl_certificate(host: str, port: int) -> bool:
        """验证SSL证书（简单实现）"""
        import ssl
        import socket
        
        try:
            context = ssl.create_default_context()
            with socket.create_connection((host, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=host) as ssock:
                    # 如果能成功建立SSL连接，说明证书有效
                    return True
        except Exception as e:
            logger.warning(f"SSL证书验证失败 {host}:{port} - {e}")
            return False
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理文件名，移除不安全字符"""
        import re
        
        # 移除或替换不安全的字符
        unsafe_chars = r'[<>:"/\\|?*\x00-\x1f]'
        safe_filename = re.sub(unsafe_chars, '_', filename)
        
        # 移除开头和结尾的点和空格
        safe_filename = safe_filename.strip('. ')
        
        # 限制长度
        if len(safe_filename) > 255:
            safe_filename = safe_filename[:255]
        
        return safe_filename or 'unnamed_file'
