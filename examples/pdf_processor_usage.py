#!/usr/bin/env python3
"""
PDF处理器使用示例

展示重构后的PDF处理器的各种使用方法和功能特性。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到sys.path
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

def example_basic_usage():
    """基本使用示例"""
    print("📋 基本使用示例")
    print("-" * 50)
    
    from src.processors.pdf_processor import PdfProcessor
    from src.core.ai_analyzer import AnalysisResult
    
    # 创建PDF处理器（使用默认配置）
    processor = PdfProcessor()
    
    # 创建模拟的AI分析结果
    analysis = AnalysisResult(
        category="工作",
        priority="中",
        sentiment="中性",
        summary="包含PDF附件的邮件",
        action_required=True,
        suggested_actions=["处理PDF文档"],
        processor_suggestions=[],
        confidence=0.9
    )
    
    # 模拟邮件数据
    email_data = {
        'sender': '<EMAIL>',
        'subject': '重要文档',
        'body': '请查看附件中的PDF文档',
        'attachments': [
            {
                'filename': 'document.pdf',
                'content_type': 'application/pdf',
                'content': b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n>>\nendobj\nxref\n0 1\n0000000000 65535 f \ntrailer\n<<\n/Size 1\n/Root 1 0 R\n>>\nstartxref\n9\n%%EOF'
            }
        ]
    }
    
    # 检查是否可以处理
    if processor.can_process(email_data, analysis):
        print("✅ 检测到PDF附件，开始处理...")
        
        # 处理邮件（注意：这里会尝试发送邮件，在示例中可能失败）
        result = processor.process(email_data, analysis)
        
        print(f"📊 处理结果:")
        print(f"   状态: {result['status']}")
        if 'summary' in result:
            summary = result['summary']
            print(f"   总计处理: {summary['total_processed']}")
            print(f"   成功: {summary['successful']}")
            print(f"   失败: {summary['failed']}")
            print(f"   处理时间: {summary['processing_time']:.2f}秒")
    else:
        print("❌ 未检测到PDF附件")

def example_custom_configuration():
    """自定义配置示例"""
    print("\n📋 自定义配置示例")
    print("-" * 50)
    
    from src.processors.pdf_processor import PdfProcessor
    
    # 自定义配置
    config = {
        'enabled': True,
        'max_file_size': 10 * 1024 * 1024,  # 10MB限制
        'auto_reply': False  # 禁用自动回复
    }
    
    # 创建自定义配置的处理器
    processor = PdfProcessor("custom_pdf_processor", config)
    
    print(f"✅ 自定义PDF处理器创建成功:")
    print(f"   名称: {processor.name}")
    print(f"   启用状态: {processor.enabled}")
    print(f"   最大文件大小: {processor.max_file_size / 1024 / 1024:.1f}MB")
    print(f"   自动回复: {processor.auto_reply}")
    print(f"   统计信息: {processor.processing_stats}")

def example_component_usage():
    """组件单独使用示例"""
    print("\n📋 组件单独使用示例")
    print("-" * 50)
    
    from src.processors.pdf_processor import (
        PDFMetadataExtractor,
        PDFTextExtractor,
        PDFTextAnalyzer,
        TextExtractionMethod
    )
    
    # 1. 元数据提取器
    print("🔍 元数据提取器:")
    metadata_extractor = PDFMetadataExtractor()
    print(f"   可用提取方法: {len(metadata_extractor.extraction_methods)}")
    
    # 2. 文本提取器
    print("📝 文本提取器:")
    text_extractor = PDFTextExtractor()
    print(f"   可用提取方法: {len(text_extractor.extraction_methods)}")
    
    # 3. 文本分析器
    print("📊 文本分析器:")
    text_analyzer = PDFTextAnalyzer()
    
    # 示例文本分析
    sample_text = """
    这是一个PDF文档的示例文本。
    
    文档包含多个段落，用于演示文本分析功能。
    
    关键词包括：PDF、文档、分析、处理、示例。
    
    表格数据：
    | 项目 | 数值 |
    |------|------|
    | A    | 100  |
    | B    | 200  |
    
    图片说明：图1显示了处理流程。
    """
    
    analysis_result = text_analyzer.analyze_text(
        sample_text, 
        TextExtractionMethod.FALLBACK, 
        0.8
    )
    
    print(f"   文本分析结果:")
    print(f"     字数: {analysis_result.word_count}")
    print(f"     字符数: {analysis_result.char_count}")
    print(f"     段落数: {analysis_result.paragraph_count}")
    print(f"     语言: {analysis_result.language}")
    print(f"     关键词: {', '.join(analysis_result.keywords[:5])}")
    print(f"     包含表格: {analysis_result.has_tables}")
    print(f"     包含图片: {analysis_result.has_images}")

def example_error_handling():
    """错误处理示例"""
    print("\n📋 错误处理示例")
    print("-" * 50)
    
    from src.processors.pdf_processor import (
        validate_pdf_file,
        PDFFileTooLargeError,
        PDFCorruptedError,
        calculate_file_hash
    )
    
    # 1. 文件验证示例
    print("🔒 文件验证:")
    
    # 有效PDF
    valid_pdf = b'%PDF-1.4\nvalid content'
    try:
        validate_pdf_file(valid_pdf, "valid.pdf")
        print("   ✅ 有效PDF验证通过")
        print(f"   📋 文件哈希: {calculate_file_hash(valid_pdf)[:16]}...")
    except Exception as e:
        print(f"   ❌ 验证失败: {e}")
    
    # 无效文件
    invalid_file = b'This is not a PDF'
    try:
        validate_pdf_file(invalid_file, "invalid.txt")
        print("   ❌ 无效文件验证应该失败")
    except PDFCorruptedError as e:
        print(f"   ✅ 正确检测到损坏文件: {e}")
    
    # 过大文件
    large_file = b'%PDF-1.4' + b'x' * (60 * 1024 * 1024)  # 60MB
    try:
        validate_pdf_file(large_file, "large.pdf")
        print("   ❌ 大文件验证应该失败")
    except PDFFileTooLargeError as e:
        print(f"   ✅ 正确检测到文件过大: {e}")

def example_attachment_detection():
    """附件检测示例"""
    print("\n📋 附件检测示例")
    print("-" * 50)
    
    from src.processors.pdf_processor import PdfProcessor
    from src.core.ai_analyzer import AnalysisResult
    
    processor = PdfProcessor()
    analysis = AnalysisResult(
        category="测试",
        priority="低",
        sentiment="中性",
        summary="测试邮件",
        action_required=False,
        suggested_actions=[],
        processor_suggestions=[],
        confidence=0.5
    )
    
    # 测试不同类型的附件
    test_cases = [
        {
            'name': 'PDF文件（标准MIME类型）',
            'attachment': {
                'filename': 'document.pdf',
                'content_type': 'application/pdf',
                'content': b'%PDF-1.4'
            }
        },
        {
            'name': 'PDF文件（扩展名检测）',
            'attachment': {
                'filename': 'report.pdf',
                'content_type': 'application/octet-stream',
                'content': b'%PDF-1.4'
            }
        },
        {
            'name': '文本文件',
            'attachment': {
                'filename': 'readme.txt',
                'content_type': 'text/plain',
                'content': b'text content'
            }
        },
        {
            'name': '图片文件',
            'attachment': {
                'filename': 'image.jpg',
                'content_type': 'image/jpeg',
                'content': b'jpeg content'
            }
        }
    ]
    
    for test_case in test_cases:
        email_data = {
            'sender': '<EMAIL>',
            'subject': '测试邮件',
            'body': '测试内容',
            'attachments': [test_case['attachment']]
        }
        
        can_process = processor.can_process(email_data, analysis)
        status = "✅ 可处理" if can_process else "❌ 跳过"
        print(f"   {test_case['name']}: {status}")

def main():
    """运行所有示例"""
    print("🚀 PDF处理器使用示例")
    print("=" * 60)
    
    try:
        example_basic_usage()
        example_custom_configuration()
        example_component_usage()
        example_error_handling()
        example_attachment_detection()
        
        print("\n🎉 所有示例运行完成！")
        print("\n💡 提示:")
        print("   - 在生产环境中使用时，请确保配置正确的SMTP设置")
        print("   - 建议根据实际需求调整文件大小限制")
        print("   - 可以通过配置禁用自动回复功能")
        print("   - 支持docling和PyPDF2两种PDF解析引擎")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
