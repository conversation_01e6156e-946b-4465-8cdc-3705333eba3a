# 快速开始指南

## 🚀 第一次使用

### 1. 安装依赖

使用启动脚本自动安装所有依赖：

```bash
./run.sh install
```

或者手动安装：

```bash
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

### 2. 配置邮箱和AI

复制配置模板：

```bash
cp config/config.example.yaml config/config.yaml
```

编辑 `config/config.yaml`，填入您的信息：

```yaml
# 邮箱配置
email:
  host: "imap.gmail.com"          # 您的邮箱服务器
  username: "<EMAIL>" # 您的邮箱
  password: "your-app-password"    # 应用专用密码

# AI配置  
ai:
  provider: "openai"              # 或 "anthropic"
  api_key: "your-api-key"         # 您的API密钥
  model: "gpt-3.5-turbo"          # 模型名称
```

### 3. 测试连接

```bash
./run.sh test
```

### 4. 查看状态

```bash
./run.sh status
```

### 5. 运行机器人

```bash
# 运行一次处理
./run.sh once

# 启动持续运行
./run.sh start
```

## 📧 邮箱配置说明

### Gmail 配置

1. 启用两步验证
2. 生成应用专用密码：
   - 访问 [Google账户安全设置](https://myaccount.google.com/security)
   - 点击"应用专用密码"
   - 生成新密码并在配置中使用

```yaml
email:
  host: "imap.gmail.com"
  port: 993
  username: "<EMAIL>"
  password: "your-app-password"  # 使用应用专用密码
  use_ssl: true
  protocol: "imap"
```

### Outlook 配置

```yaml
email:
  host: "outlook.office365.com"
  port: 993
  username: "<EMAIL>"
  password: "your-password"
  use_ssl: true
  protocol: "imap"
```

## 🤖 AI配置说明

### OpenAI

```yaml
ai:
  provider: "openai"
  api_key: "sk-..."
  model: "gpt-3.5-turbo"  # 或 "gpt-4"
```

### Anthropic

```yaml
ai:
  provider: "anthropic"
  api_key: "sk-ant-..."
  model: "claude-3-sonnet-********"
```

## 🔧 常用命令

```bash
# 显示帮助
./run.sh help

# 安装依赖
./run.sh install

# 测试邮箱连接
./run.sh test

# 显示状态信息
./run.sh status

# 运行一次处理
./run.sh once

# 启动机器人（持续运行）
./run.sh start
```

## 📝 日志文件

- 主日志: `logs/mailer.log`
- 错误日志: `logs/mailer_error.log`
- 处理日志: `logs/email_processing.log`

## 🔍 故障排除

### 邮箱连接失败

1. 检查邮箱服务器地址和端口
2. 确认用户名和密码正确
3. 检查是否启用了IMAP
4. 确认防火墙设置

### AI分析失败

1. 检查API密钥是否正确
2. 确认网络连接正常
3. 检查API配额是否用完

### 处理器错误

查看日志文件了解详细错误信息：

```bash
tail -f logs/mailer.log
```

## 🎯 下一步

1. 根据需要自定义处理器
2. 调整配置参数
3. 设置定时任务
4. 编写测试用例

更多详细信息请参考 [README.md](README.md)。
