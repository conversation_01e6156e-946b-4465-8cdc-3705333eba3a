"""
AI回复处理器 - 使用AI生成邮件回复内容并发送
"""

import os
import sys
from typing import Dict, Any, Optional
from loguru import logger

# 添加项目根目录到sys.path
base_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(base_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.core.processor_manager import BaseProcessor
from src.core.ai_analyzer import AnalysisResult
from src.utils.smtp_sender import send_email

class AIReplyProcessor(BaseProcessor):
    """AI回复处理器 - 使用AI生成邮件回复并发送"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.enabled = config.get('enabled', False) if config else False
        self.ai_prompt = config.get('ai_prompt', "请根据以下邮件内容生成合适的回复：") if config else "请根据以下邮件内容生成合适的回复："
        
    def can_process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> bool:
        """判断是否需要AI回复"""
        if not self.enabled:
            return False
            
        # 检查是否需要行动
        if not analysis.action_required:
            return False
            
        # 避免回复自动生成的邮件
        subject = email_data.get('subject', '').lower()
        if any(keyword in subject for keyword in ['re:', 'fwd:', 'auto-reply', 'out of office']):
            return False
            
        return True
    
    def process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """使用AI生成回复并发送"""
        try:
            # 构建AI提示
            prompt = self._build_ai_prompt(email_data)
            
            # 在实际应用中，这里调用AI服务生成回复
            # 示例中使用固定回复，实际应替换为AI调用
            ai_reply = self._call_ai_service(prompt)  # 实际实现中应调用真正的AI服务
            
            # 发送回复邮件
            self._send_reply_email(
                recipient=email_data.get('sender', ''),
                subject=f"Re: {email_data.get('subject', '')}",
                content=ai_reply
            )
            
            return {
                "reply_sent": True,
                "recipient": email_data.get('sender', ''),
                "ai_reply": ai_reply
            }
            
        except Exception as e:
            logger.error(f"AI回复处理失败: {e}")
            return {
                "reply_sent": False,
                "error": str(e)
            }
    
    def _build_ai_prompt(self, email_data: Dict[str, Any]) -> str:
        """构建AI提示词"""
        return f"""
        {self.ai_prompt}
        
        邮件主题: {email_data.get('subject', '')}
        发件人: {email_data.get('sender', '')}
        邮件内容:
        {email_data.get('body', '')}
        """
    
    def _call_ai_service(self, prompt: str) -> str:
        """调用AI服务生成回复（示例实现）"""
        # 在实际应用中，这里应调用真正的AI服务
        # 示例中使用固定回复
        return "感谢您的邮件！我已收到您的消息，会尽快处理并回复您。"
    
    def _send_reply_email(self, recipient: str, subject: str, content: str):
        """发送回复邮件"""
        logger.info(f"准备发送AI回复给: {recipient}")
        logger.info(f"主题: {subject}")
        logger.info(f"内容: {content}")

        # 使用SMTP发送邮件
        try:
            success = send_email(
                to=recipient,
                subject=subject,
                body=content
            )

            if success:
                logger.info(f"AI回复邮件发送成功: {recipient}")
            else:
                logger.error(f"AI回复邮件发送失败: {recipient}")

        except Exception as e:
            logger.error(f"发送AI回复邮件时出错: {e}")
            # 如果SMTP发送失败，记录详细信息但不中断处理
            logger.info(f"邮件发送失败，但已记录处理日志")
    
    def get_priority(self) -> int:
        """AI回复处理器优先级较高"""
        return 30
    
    def validate_config(self) -> bool:
        """验证配置"""
        return True