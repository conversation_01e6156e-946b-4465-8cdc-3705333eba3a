"""
通知处理器 - 发送重要邮件通知
"""

import os
import sys
import subprocess
from typing import Dict, Any, Optional
from loguru import logger

# 添加项目根目录到sys.path
base_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(base_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.core.processor_manager import BaseProcessor
from src.core.ai_analyzer import AnalysisResult

try:
    from src.utils.string_utils import escape_apple_script  # 导入字符串转义函数
except ImportError:
    # 如果没有string_utils，提供一个简单的转义函数
    def escape_apple_script(text: str) -> str:
        """简单的AppleScript字符串转义"""
        return text.replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r')


class NotificationProcessor(BaseProcessor):
    """通知处理器 - 对重要邮件发送系统通知"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.priority_threshold = config.get('priority_threshold', '高') if config else '高'
        self.notification_method = config.get('method', 'system') if config else 'system'
        
    def can_process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> bool:
        """只处理高优先级邮件"""
        return analysis.priority == self.priority_threshold
    
    def process(self, email_data: Dict[str, Any], analysis: AnalysisResult) -> Dict[str, Any]:
        """发送通知"""
        subject = email_data.get('subject', '无主题')
        sender = email_data.get('sender', '未知发件人')
        
        notification_title = f"重要邮件: {subject[:50]}"
        notification_body = f"发件人: {sender}\n摘要: {analysis.summary}"
        
        success = False
        method_used = ""
        
        try:
            if self.notification_method == 'system':
                success = self._send_system_notification(notification_title, notification_body)
                method_used = "系统通知"
            elif self.notification_method == 'console':
                success = self._send_console_notification(notification_title, notification_body)
                method_used = "控制台通知"
            else:
                logger.warning(f"不支持的通知方式: {self.notification_method}")
                
        except Exception as e:
            logger.error(f"发送通知失败: {e}")
            
        return {
            "notification_sent": success,
            "method": method_used,
            "title": notification_title,
            "body": notification_body
        }
    
    def _send_system_notification(self, title: str, body: str) -> bool:
        """发送系统通知"""
        try:
            # macOS
            if os.system("which osascript > /dev/null 2>&1") == 0:
                # 转义特殊字符防止AppleScript语法错误
                escaped_title = escape_apple_script(title)
                escaped_body = escape_apple_script(body)
                script = f'''
                display notification "{escaped_body}" with title "{escaped_title}"
                '''
                subprocess.run(['osascript', '-e', script], check=True)
                return True
                
            # Linux (需要安装 notify-send)
            elif os.system("which notify-send > /dev/null 2>&1") == 0:
                subprocess.run(['notify-send', title, body], check=True)
                return True
                
            # Windows (需要安装 plyer 或使用 Windows Toast)
            else:
                logger.warning("当前系统不支持系统通知，使用控制台通知")
                return self._send_console_notification(title, body)
                
        except Exception as e:
            logger.error(f"系统通知发送失败: {e}")
            return False
    
    def _send_console_notification(self, title: str, body: str) -> bool:
        """发送控制台通知"""
        try:
            print("\n" + "="*60)
            print(f"🔔 {title}")
            print("-"*60)
            print(body)
            print("="*60 + "\n")
            return True
        except Exception as e:
            logger.error(f"控制台通知发送失败: {e}")
            return False
    
    def get_priority(self) -> int:
        """通知处理器优先级较高"""
        return 10
    
    def validate_config(self) -> bool:
        """验证配置"""
        valid_methods = ['system', 'console']
        if self.notification_method not in valid_methods:
            logger.error(f"无效的通知方式: {self.notification_method}")
            return False
        return True
