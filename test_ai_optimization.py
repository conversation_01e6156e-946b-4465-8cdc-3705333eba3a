#!/usr/bin/env python3
"""
测试AI分析器优化功能
"""

import json
import sys
import os

# 添加项目根目录到sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.core.ai_analyzer import AnalysisResult
from src.core.processor_manager import ProcessorManager


def test_analysis_result_with_processor_suggestions():
    """测试带有处理器建议的分析结果"""
    print("=== 测试AnalysisResult类的processor_suggestions功能 ===")
    
    # 创建包含处理器建议的分析结果
    processor_suggestions = [
        {
            'processor_name': 'notification_processor',
            'reason': '高优先级邮件需要通知',
            'priority': 1,
            'enabled': True
        },
        {
            'processor_name': 'ai_reply_processor',
            'reason': '需要智能回复',
            'priority': 2,
            'enabled': True
        },
        {
            'processor_name': 'log_processor',
            'reason': '记录处理日志',
            'priority': 999,
            'enabled': True
        }
    ]
    
    analysis = AnalysisResult(
        category="工作",
        priority="高",
        sentiment="中性",
        summary="重要工作邮件需要处理",
        action_required=True,
        suggested_actions=["立即回复", "安排会议", "转发给相关人员"],
        processor_suggestions=processor_suggestions,
        confidence=0.9
    )
    
    print("分析结果:")
    print(json.dumps(analysis.to_dict(), ensure_ascii=False, indent=2))
    
    print(f"\n处理器建议数量: {len(analysis.processor_suggestions)}")
    for suggestion in analysis.processor_suggestions:
        print(f"- {suggestion['processor_name']}: {suggestion['reason']} (优先级: {suggestion['priority']})")
    
    return analysis


def test_ai_prompt_format():
    """测试AI提示词格式"""
    print("\n=== 测试AI提示词格式 ===")
    
    # 模拟邮件数据
    email_data = {
        'subject': '紧急：项目进度汇报',
        'sender': '<EMAIL>',
        'body': '请尽快提供本周项目进度汇报，包括完成情况和遇到的问题。',
        'attachments': []
    }
    
    # 模拟AI返回的JSON响应
    ai_response = {
        "category": "工作",
        "priority": "高",
        "sentiment": "中性",
        "summary": "经理要求提供项目进度汇报",
        "action_required": True,
        "suggested_actions": [
            "准备项目进度汇报",
            "整理本周完成情况",
            "列出遇到的问题",
            "发送回复邮件"
        ],
        "processor_suggestions": [
            {
                "processor_name": "notification_processor",
                "reason": "高优先级工作邮件需要立即通知",
                "priority": 1,
                "enabled": True
            },
            {
                "processor_name": "ai_reply_processor",
                "reason": "需要生成专业的工作回复",
                "priority": 2,
                "enabled": True
            },
            {
                "processor_name": "log_processor",
                "reason": "记录重要工作邮件处理日志",
                "priority": 999,
                "enabled": True
            }
        ],
        "confidence": 0.85
    }
    
    print("模拟AI响应:")
    print(json.dumps(ai_response, ensure_ascii=False, indent=2))
    
    # 创建AnalysisResult对象
    analysis = AnalysisResult(
        category=ai_response['category'],
        priority=ai_response['priority'],
        sentiment=ai_response['sentiment'],
        summary=ai_response['summary'],
        action_required=ai_response['action_required'],
        suggested_actions=ai_response['suggested_actions'],
        processor_suggestions=ai_response['processor_suggestions'],
        confidence=ai_response['confidence']
    )
    
    print(f"\n解析后的分析结果:")
    print(f"分类: {analysis.category}")
    print(f"优先级: {analysis.priority}")
    print(f"建议行动: {', '.join(analysis.suggested_actions)}")
    print(f"推荐处理器: {len(analysis.processor_suggestions)}个")
    
    return analysis, email_data


def test_processor_suggestions_mapping():
    """测试处理器建议映射"""
    print("\n=== 测试处理器建议映射 ===")
    
    # 不同类型邮件的处理器建议示例
    test_cases = [
        {
            "name": "PDF附件邮件",
            "suggestions": [
                {"processor_name": "pdf_processor", "reason": "处理PDF附件", "priority": 1, "enabled": True},
                {"processor_name": "ai_reply_processor", "reason": "生成PDF处理结果回复", "priority": 2, "enabled": True},
                {"processor_name": "log_processor", "reason": "记录处理日志", "priority": 999, "enabled": True}
            ]
        },
        {
            "name": "高优先级通知邮件",
            "suggestions": [
                {"processor_name": "notification_processor", "reason": "发送重要通知", "priority": 1, "enabled": True},
                {"processor_name": "auto_reply_processor", "reason": "快速确认收到", "priority": 2, "enabled": True},
                {"processor_name": "log_processor", "reason": "记录处理日志", "priority": 999, "enabled": True}
            ]
        },
        {
            "name": "技术支持邮件",
            "suggestions": [
                {"processor_name": "ai_reply_processor", "reason": "生成技术回复", "priority": 1, "enabled": True},
                {"processor_name": "log_processor", "reason": "记录技术支持日志", "priority": 999, "enabled": True}
            ]
        },
        {
            "name": "垃圾邮件",
            "suggestions": [
                {"processor_name": "log_processor", "reason": "仅记录日志，不做其他处理", "priority": 999, "enabled": True}
            ]
        }
    ]
    
    for case in test_cases:
        print(f"\n{case['name']}:")
        for suggestion in case['suggestions']:
            print(f"  - {suggestion['processor_name']}: {suggestion['reason']} (优先级: {suggestion['priority']})")
    
    return test_cases


def main():
    """主测试函数"""
    print("开始测试AI分析器优化功能...\n")
    
    try:
        # 测试1: AnalysisResult类功能
        analysis1 = test_analysis_result_with_processor_suggestions()
        
        # 测试2: AI提示词格式
        analysis2, email_data = test_ai_prompt_format()
        
        # 测试3: 处理器建议映射
        test_cases = test_processor_suggestions_mapping()
        
        print("\n=== 测试总结 ===")
        print("✅ AnalysisResult类已成功扩展processor_suggestions字段")
        print("✅ AI提示词格式已优化，包含处理器建议")
        print("✅ 处理器建议映射逻辑已设计完成")
        print("✅ 所有测试通过！")
        
        print("\n=== 下一步建议 ===")
        print("1. 在实际环境中测试AI提供商的响应")
        print("2. 验证ProcessorManager的AI建议处理逻辑")
        print("3. 调整各处理器的优先级设置")
        print("4. 监控AI建议的准确性并持续优化")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
