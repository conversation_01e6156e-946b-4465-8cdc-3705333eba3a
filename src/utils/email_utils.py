"""
邮件工具类 - 提供邮件处理相关的辅助功能
"""

import re
import html
from typing import List, Dict, Any, Optional
from email.utils import parseaddr, formataddr
from urllib.parse import urlparse


class EmailUtils:
    """邮件工具类"""
    
    @staticmethod
    def extract_email_address(email_string: str) -> str:
        """从邮件字符串中提取邮件地址"""
        name, address = parseaddr(email_string)
        return address
    
    @staticmethod
    def extract_name(email_string: str) -> str:
        """从邮件字符串中提取姓名"""
        name, address = parseaddr(email_string)
        return name if name else address.split('@')[0]
    
    @staticmethod
    def format_email_address(name: str, email: str) -> str:
        """格式化邮件地址"""
        return formataddr((name, email))
    
    @staticmethod
    def is_valid_email(email: str) -> bool:
        """验证邮件地址格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def extract_urls(text: str) -> List[str]:
        """从文本中提取URL"""
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        return re.findall(url_pattern, text)
    
    @staticmethod
    def extract_phone_numbers(text: str) -> List[str]:
        """从文本中提取电话号码"""
        # 匹配常见的电话号码格式
        patterns = [
            r'\b\d{3}-\d{3}-\d{4}\b',  # ************
            r'\b\d{3}\.\d{3}\.\d{4}\b',  # ************
            r'\b\d{3}\s\d{3}\s\d{4}\b',  # ************
            r'\(\d{3}\)\s?\d{3}-\d{4}',  # (*************
            r'\+\d{1,3}\s?\d{3,4}\s?\d{3,4}\s?\d{3,4}',  # 国际格式
        ]
        
        phone_numbers = []
        for pattern in patterns:
            phone_numbers.extend(re.findall(pattern, text))
        
        return phone_numbers
    
    @staticmethod
    def clean_html(html_content: str) -> str:
        """清理HTML内容，提取纯文本"""
        # 移除HTML标签
        clean_text = re.sub(r'<[^>]+>', '', html_content)
        # 解码HTML实体
        clean_text = html.unescape(clean_text)
        # 移除多余的空白字符
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()
        return clean_text
    
    @staticmethod
    def extract_quoted_text(email_body: str) -> Dict[str, str]:
        """分离邮件正文和引用内容"""
        # 常见的引用标识符
        quote_patterns = [
            r'On .* wrote:',
            r'From:.*\nSent:.*\nTo:.*\nSubject:',
            r'-----Original Message-----',
            r'________________________________',
            r'> ',  # 引用符号
        ]
        
        original_text = email_body
        quoted_text = ""
        
        for pattern in quote_patterns:
            match = re.search(pattern, email_body, re.IGNORECASE | re.MULTILINE)
            if match:
                split_pos = match.start()
                original_text = email_body[:split_pos].strip()
                quoted_text = email_body[split_pos:].strip()
                break
        
        return {
            "original": original_text,
            "quoted": quoted_text
        }
    
    @staticmethod
    def detect_language(text: str) -> str:
        """简单的语言检测"""
        # 检测中文字符
        chinese_pattern = r'[\u4e00-\u9fff]'
        if re.search(chinese_pattern, text):
            return "zh"
        
        # 检测日文字符
        japanese_pattern = r'[\u3040-\u309f\u30a0-\u30ff]'
        if re.search(japanese_pattern, text):
            return "ja"
        
        # 检测韩文字符
        korean_pattern = r'[\uac00-\ud7af]'
        if re.search(korean_pattern, text):
            return "ko"
        
        # 默认为英文
        return "en"
    
    @staticmethod
    def extract_keywords(text: str, min_length: int = 3) -> List[str]:
        """提取关键词"""
        # 移除标点符号和特殊字符
        clean_text = re.sub(r'[^\w\s]', ' ', text.lower())
        
        # 分割单词
        words = clean_text.split()
        
        # 过滤短词和常见停用词
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we',
            'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her',
            'its', 'our', 'their'
        }
        
        keywords = [
            word for word in words 
            if len(word) >= min_length and word not in stop_words
        ]
        
        # 去重并保持顺序
        seen = set()
        unique_keywords = []
        for keyword in keywords:
            if keyword not in seen:
                seen.add(keyword)
                unique_keywords.append(keyword)
        
        return unique_keywords
    
    @staticmethod
    def is_auto_generated(email_data: Dict[str, Any]) -> bool:
        """判断是否为自动生成的邮件"""
        subject = email_data.get('subject', '').lower()
        sender = email_data.get('sender', '').lower()
        body = email_data.get('body', '').lower()
        
        # 检查主题中的自动生成标识
        auto_subject_keywords = [
            'auto-reply', 'automatic reply', 'out of office', 'vacation',
            'delivery failure', 'undelivered', 'bounce', 'mailer-daemon',
            'no-reply', 'noreply', 'do not reply'
        ]
        
        for keyword in auto_subject_keywords:
            if keyword in subject:
                return True
        
        # 检查发件人
        auto_sender_keywords = [
            'noreply', 'no-reply', 'mailer-daemon', 'postmaster',
            'auto-reply', 'automatic'
        ]
        
        for keyword in auto_sender_keywords:
            if keyword in sender:
                return True
        
        # 检查邮件头中的自动回复标识
        headers = email_data.get('headers', {})
        if headers.get('Auto-Submitted') or headers.get('X-Auto-Response-Suppress'):
            return True
        
        return False
    
    @staticmethod
    def calculate_text_similarity(text1: str, text2: str) -> float:
        """计算两个文本的相似度（简单实现）"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 and not words2:
            return 1.0
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
        
    @staticmethod
    def send_email(to: str, subject: str, body: str,
                  config: Optional[Dict[str, Any]] = None) -> bool:
        """发送邮件"""
        if not config:
            from ..core.config_manager import ConfigManager
            config = ConfigManager.get_email_config()
            
        try:
            # 简化实现，实际应使用SMTP库发送邮件
            print(f"发送邮件到: {to}")
            print(f"主题: {subject}")
            print(f"内容: {body}")
            return True
        except Exception as e:
            print(f"发送邮件失败: {e}")
            return False
