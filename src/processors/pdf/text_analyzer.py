"""
PDF文本分析器

负责分析PDF文本内容，提取关键信息和统计数据。
"""

import re
from typing import List
from collections import Counter

from .models import PDFTextAnalysis, TextExtractionMethod


class PDFTextAnalyzer:
    """PDF文本分析器"""
    
    def __init__(self):
        pass
    
    def analyze_text(self, text: str, extraction_method: TextExtractionMethod, 
                    confidence: float) -> PDFTextAnalysis:
        """
        分析PDF文本内容
        
        Args:
            text: 文本内容
            extraction_method: 提取方法
            confidence: 提取置信度
            
        Returns:
            PDFTextAnalysis: 文本分析结果
        """
        analysis = PDFTextAnalysis(
            text_content=text,
            extraction_method=extraction_method,
            extraction_confidence=confidence
        )
        
        if not text:
            return analysis
        
        # 基本统计
        analysis.char_count = len(text)
        analysis.char_count_no_spaces = len(text.replace(' ', '').replace('\n', '').replace('\t', ''))
        
        lines = text.split('\n')
        analysis.line_count = len(lines)
        
        words = text.split()
        analysis.word_count = len(words)
        
        # 段落统计
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        analysis.paragraph_count = len(paragraphs)
        
        # 语言检测
        analysis.language = self._detect_language(text)
        
        # 关键词提取
        analysis.keywords = self._extract_keywords(text)
        
        # 内容特征检测
        analysis.has_tables = self._detect_tables(text)
        analysis.has_images = self._detect_images(text)
        
        # 文本预览
        analysis.text_preview = self._generate_preview(text)
        
        return analysis
    
    def _detect_language(self, text: str) -> str:
        """
        检测文本语言
        
        Args:
            text: 文本内容
            
        Returns:
            str: 语言类型
        """
        # 检测中文字符
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        # 检测英文单词
        english_words = len(re.findall(r'[a-zA-Z]+', text))
        # 检测数字
        numbers = len(re.findall(r'\d+', text))
        
        total_chars = len(text)
        if total_chars == 0:
            return "unknown"
        
        chinese_ratio = chinese_chars / total_chars
        english_ratio = english_words / total_chars
        
        if chinese_ratio > 0.1:
            return "中文"
        elif english_ratio > 0.3:
            return "英文"
        elif numbers / total_chars > 0.5:
            return "数据"
        else:
            return "混合"
    
    def _extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """
        提取关键词
        
        Args:
            text: 文本内容
            max_keywords: 最大关键词数量
            
        Returns:
            List[str]: 关键词列表
        """
        # 清理文本
        clean_text = re.sub(r'[^\w\s]', ' ', text.lower())
        clean_text = re.sub(r'\d+', ' ', clean_text)
        
        # 分词
        words = [word for word in clean_text.split() if len(word) > 3]
        
        # 移除常见停用词
        stop_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'this', 'that', 'these', 'those', 'is', 'are', 'was', 'were', 'be', 'been',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'can', 'may', 'might', 'must', 'shall', 'should', 'would', 'could'
        }
        
        filtered_words = [word for word in words if word not in stop_words]
        
        # 统计词频
        word_freq = Counter(filtered_words)
        
        # 返回最常见的关键词
        keywords = [word for word, count in word_freq.most_common(max_keywords) if count > 1]
        
        return keywords[:max_keywords] if keywords else ["无关键词"]
    
    def _detect_tables(self, text: str) -> bool:
        """
        检测是否包含表格
        
        Args:
            text: 文本内容
            
        Returns:
            bool: 是否包含表格
        """
        table_indicators = [
            '表格', 'Table', '表', '┌', '┐', '└', '┘', '├', '┤', '┬', '┴', '┼',
            '|', '─', '━', '═', '║', '╔', '╗', '╚', '╝', '╠', '╣', '╦', '╩', '╬'
        ]
        
        for indicator in table_indicators:
            if indicator in text:
                return True
        
        # 检测制表符分隔的内容
        lines = text.split('\n')
        tab_lines = sum(1 for line in lines if '\t' in line and line.count('\t') > 2)
        
        return tab_lines > 3
    
    def _detect_images(self, text: str) -> bool:
        """
        检测是否包含图片引用
        
        Args:
            text: 文本内容
            
        Returns:
            bool: 是否包含图片引用
        """
        image_indicators = [
            '图', 'Figure', 'Fig', 'Image', 'Picture', 'Photo', '图片', '图像',
            '插图', '示意图', '流程图', '图表'
        ]
        
        for indicator in image_indicators:
            if indicator in text:
                return True
        
        return False
    
    def _generate_preview(self, text: str, max_length: int = 200) -> str:
        """
        生成文本预览
        
        Args:
            text: 文本内容
            max_length: 最大预览长度
            
        Returns:
            str: 文本预览
        """
        if len(text) <= max_length:
            return text
        
        # 尝试在句号处截断
        preview = text[:max_length]
        last_period = preview.rfind('。')
        if last_period > max_length // 2:
            return preview[:last_period + 1]
        
        last_period = preview.rfind('.')
        if last_period > max_length // 2:
            return preview[:last_period + 1]
        
        # 否则直接截断并添加省略号
        return preview + "..."
