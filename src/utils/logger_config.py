"""
日志配置工具 - 配置应用日志系统
"""

import os
import sys
from typing import Optional
from loguru import logger
from ..core.config_manager import LogConfig


def setup_logger(config: LogConfig):
    """设置日志配置"""
    
    # 移除默认的日志处理器
    logger.remove()
    
    # 确保日志目录存在
    log_dir = os.path.dirname(config.file_path)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # 控制台日志格式
    console_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 文件日志格式
    file_format = (
        "{time:YYYY-MM-DD HH:mm:ss} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{message}"
    )
    
    # 添加控制台日志处理器
    logger.add(
        sys.stdout,
        format=console_format,
        level=config.level,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 添加文件日志处理器
    logger.add(
        config.file_path,
        format=file_format,
        level=config.level,
        rotation=config.max_size,
        retention=config.retention,
        compression="zip",
        backtrace=True,
        diagnose=True,
        encoding="utf-8"
    )
    
    # 添加错误日志文件
    error_log_path = config.file_path.replace('.log', '_error.log')
    logger.add(
        error_log_path,
        format=file_format,
        level="ERROR",
        rotation=config.max_size,
        retention=config.retention,
        compression="zip",
        backtrace=True,
        diagnose=True,
        encoding="utf-8"
    )
    
    logger.info("日志系统初始化完成")
    logger.info(f"日志级别: {config.level}")
    logger.info(f"日志文件: {config.file_path}")
    logger.info(f"错误日志: {error_log_path}")


def get_logger(name: Optional[str] = None):
    """获取日志记录器
    
    此函数用于获取配置好的日志记录器实例。如果提供了name参数，
    则返回一个绑定了该名称的日志记录器，这有助于在日志消息中标识不同的模块或组件。
    如果没有提供name参数，则返回全局默认的日志记录器。
    
    参数:
        name (str, optional): 日志记录器的名称，通常是模块或组件的标识。
                             此名称将在日志输出中显示，有助于区分不同来源的日志消息。
                             默认为None，表示使用全局默认日志记录器。
    
    返回:
        Logger: 配置好的loguru.Logger实例，可用于记录不同级别的日志消息。
    
    示例:
        >>> logger = get_logger("email_client")
        >>> logger.info("开始处理邮件")
        >>> logger.error("邮件发送失败", exc_info=True)
        
        # 使用默认日志记录器
        >>> default_logger = get_logger()
        >>> default_logger.debug("系统初始化完成")
    """
    if name:
        return logger.bind(name=name)
    return logger
