"""
PDF处理数据模型和枚举定义

包含PDF处理过程中使用的所有数据类和枚举类型。
"""

from datetime import datetime
from typing import Optional, List
from dataclasses import dataclass, field
from enum import Enum


# ==================== 枚举类定义 ====================

class PDFProcessingStatus(Enum):
    """PDF处理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"


class TextExtractionMethod(Enum):
    """文本提取方法枚举"""
    DOCLING = "docling"
    PYPDF2 = "pypdf2"
    FALLBACK = "fallback"


# ==================== 数据类定义 ====================

@dataclass
class PDFMetadata:
    """PDF元数据信息"""
    title: Optional[str] = None
    author: Optional[str] = None
    subject: Optional[str] = None
    creator: Optional[str] = None
    producer: Optional[str] = None
    creation_date: Optional[datetime] = None
    modification_date: Optional[datetime] = None
    page_count: int = 0
    is_encrypted: bool = False
    file_size: int = 0
    file_hash: Optional[str] = None


@dataclass
class PDFTextAnalysis:
    """PDF文本分析结果"""
    text_content: str = ""
    word_count: int = 0
    char_count: int = 0
    char_count_no_spaces: int = 0
    line_count: int = 0
    paragraph_count: int = 0
    language: str = "unknown"
    keywords: List[str] = field(default_factory=list)
    has_tables: bool = False
    has_images: bool = False
    text_preview: str = ""
    extraction_method: TextExtractionMethod = TextExtractionMethod.FALLBACK
    extraction_confidence: float = 0.0


@dataclass
class PDFProcessingResult:
    """PDF处理结果"""
    filename: str
    status: PDFProcessingStatus
    metadata: Optional[PDFMetadata] = None
    text_analysis: Optional[PDFTextAnalysis] = None
    error_message: Optional[str] = None
    processing_time: float = 0.0
    reply_sent: bool = False
    created_at: datetime = field(default_factory=datetime.now)
