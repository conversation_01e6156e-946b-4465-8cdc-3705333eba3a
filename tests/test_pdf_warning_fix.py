"""
测试PDF处理警告修复效果

验证numpy RuntimeWarning是否被正确抑制。
"""

import os
import sys
import warnings
import tempfile
from pathlib import Path

# 添加项目根目录到sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.processors.pdf import (
    PdfProcessor, 
    PDFWarningManager, 
    WarningCollector,
    suppress_pdf_warnings
)
from src.core.ai_analyzer import AnalysisResult


def create_test_pdf() -> bytes:
    """创建一个简单的测试PDF文件"""
    # 这是一个最小的PDF文件内容
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Hello World) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF"""
    return pdf_content


def test_warning_manager():
    """测试警告管理器功能"""
    print("🧪 测试警告管理器功能")
    print("-" * 50)
    
    # 测试基本功能
    with PDFWarningManager() as manager:
        print(f"✅ 警告管理器创建成功，抑制了 {len(manager.suppressed_warnings)} 类警告")
    
    # 测试警告收集器
    with WarningCollector() as collector:
        # 触发一个测试警告
        warnings.warn("这是一个测试警告", RuntimeWarning)
        
    summary = collector.get_warnings_summary()
    print(f"✅ 警告收集器收集到 {summary['total']} 个警告")
    
    print()


def test_decorator():
    """测试装饰器功能"""
    print("🧪 测试装饰器功能")
    print("-" * 50)
    
    @suppress_pdf_warnings
    def function_with_warnings():
        """会产生警告的函数"""
        import numpy as np
        # 这会产生 "Mean of empty slice" 警告
        try:
            result = np.nanmean([])
            return result
        except:
            return None
    
    result = function_with_warnings()
    print(f"✅ 装饰器测试完成，结果: {result}")
    print()


def test_pdf_processing_with_warning_fix():
    """测试PDF处理过程中的警告修复"""
    print("🧪 测试PDF处理警告修复")
    print("-" * 50)
    
    # 创建测试数据
    pdf_content = create_test_pdf()
    
    email_data = {
        'sender': '<EMAIL>',
        'subject': '测试PDF处理警告修复',
        'attachments': [
            {
                'filename': 'test_document.pdf',
                'content': pdf_content,
                'content_type': 'application/pdf'
            }
        ]
    }
    
    analysis = AnalysisResult(
        category="测试",
        priority="低",
        sentiment="中性",
        summary="测试PDF处理",
        action_required=False,
        suggested_actions=[],
        processor_suggestions=[],
        confidence=0.8
    )
    
    # 创建PDF处理器
    processor = PdfProcessor(config={'auto_reply': False})
    
    # 检查是否可以处理
    can_process = processor.can_process(email_data, analysis)
    print(f"✅ PDF处理器可以处理此邮件: {can_process}")
    
    if can_process:
        # 使用警告收集器监控处理过程
        with WarningCollector() as collector:
            result = processor.process(email_data, analysis)
            
        # 检查处理结果
        print(f"✅ PDF处理状态: {result['status']}")
        print(f"✅ 处理摘要: {result.get('summary', {})}")
        
        # 检查警告情况
        warning_summary = collector.get_warnings_summary()
        print(f"✅ 处理过程中的警告数量: {warning_summary['total']}")
        
        if warning_summary['total'] > 0:
            print(f"⚠️  警告分类: {warning_summary['by_category']}")
        else:
            print("🎉 没有收集到任何警告！")
    
    print()


def test_numpy_warning_suppression():
    """专门测试numpy警告抑制"""
    print("🧪 测试numpy警告抑制")
    print("-" * 50)
    
    import numpy as np
    
    # 不使用警告管理器的情况
    print("📋 不使用警告管理器:")
    with WarningCollector() as collector:
        try:
            # 这应该产生警告
            result1 = np.nanmean([])
        except:
            pass
    
    summary1 = collector.get_warnings_summary()
    print(f"   收集到 {summary1['total']} 个警告")
    
    # 使用警告管理器的情况
    print("📋 使用警告管理器:")
    with WarningCollector() as collector:
        with PDFWarningManager():
            try:
                # 这应该不产生警告
                result2 = np.nanmean([])
            except:
                pass
    
    summary2 = collector.get_warnings_summary()
    print(f"   收集到 {summary2['total']} 个警告")
    
    if summary2['total'] < summary1['total']:
        print("🎉 警告抑制成功！")
    else:
        print("⚠️  警告抑制可能未完全生效")
    
    print()


def main():
    """主测试函数"""
    print("🔧 PDF处理警告修复测试")
    print("=" * 60)
    print()
    
    try:
        # 运行各项测试
        test_warning_manager()
        test_decorator()
        test_numpy_warning_suppression()
        test_pdf_processing_with_warning_fix()
        
        print("🎉 所有测试完成！")
        print()
        print("📋 修复总结:")
        print("   ✅ 创建了专门的警告管理器")
        print("   ✅ 在PDF处理过程中抑制numpy警告")
        print("   ✅ 提供了警告收集和分析功能")
        print("   ✅ 保持了错误处理的完整性")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
