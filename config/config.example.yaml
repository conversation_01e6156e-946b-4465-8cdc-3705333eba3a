# 邮件处理机器人配置文件示例
# 复制此文件为 config.yaml 并填入您的实际配置

# 邮箱配置
email:
  host: "imap.gmail.com"          # 邮箱服务器地址
  port: 993                       # 端口号 (IMAP: 993/143, POP3: 995/110)
  username: "<EMAIL>" # 邮箱用户名
  password: "your-app-password"    # 邮箱密码或应用专用密码
  use_ssl: true                   # 是否使用SSL加密
  protocol: "imap"                # 协议类型: imap 或 pop3

# AI分析配置
ai:
  provider: "openai"              # AI提供商: openai 或 anthropic
  api_key: "your-api-key"         # API密钥
  model: "gpt-3.5-turbo"          # 模型名称
  base_url: null                  # 自定义API地址 (可选)
  max_tokens: 1000                # 最大token数
  temperature: 0.7                # 温度参数 (0-1)

# 调度器配置
scheduler:
  check_interval: 300             # 检查间隔(秒) - 5分钟
  max_emails_per_check: 50        # 每次检查最大邮件数
  enable_scheduler: true          # 是否启用定时调度

# 日志配置
log:
  level: "INFO"                   # 日志级别: DEBUG, INFO, WARNING, ERROR
  file_path: "logs/mailer.log"    # 日志文件路径
  max_size: "10 MB"               # 单个日志文件最大大小
  retention: "30 days"            # 日志保留时间

# 处理器配置
processors:
  # 日志处理器
  log_processor:
    enabled: true
    log_file: "logs/email_processing.log"
  
  # 通知处理器
  notification_processor:
    enabled: true
    priority_threshold: "高"       # 触发通知的优先级阈值
    method: "system"              # 通知方式: system 或 console
  
  # 自动回复处理器
  auto_reply_processor:
    enabled: false                # 默认禁用自动回复
    auto_reply_enabled: false
    enabled_categories:           # 启用自动回复的邮件分类
      - "工作"
    reply_templates:              # 回复模板
      "工作": "感谢您的邮件。我已收到您的消息，会尽快回复。"
      "个人": "谢谢您的邮件，我会及时查看并回复。"
      "营销": "感谢您的信息，如有需要我会联系您。"
      
  # AI回复处理器
  ai_reply_processor:
    enabled: false                # 是否启用AI回复
    ai_prompt: "请根据以下邮件内容生成合适的回复："  # AI提示词
    # 可选配置项:
    # max_tokens: 500             # 最大token数限制
    # temperature: 0.7            # 响应创造性 (0-1)

# 安全配置
security:
  use_keyring: true               # 是否使用系统密钥环存储密码
  encrypt_config: false          # 是否加密配置文件
  
# 邮件过滤规则 (可选)
filters:
  # 跳过的发件人
  skip_senders:
    - "noreply@"
    - "no-reply@"
    - "mailer-daemon@"
  
  # 跳过的主题关键词
  skip_subjects:
    - "unsubscribe"
    - "auto-reply"
    - "out of office"
  
  # 只处理的文件夹
  folders:
    - "INBOX"
    # - "Important"
    # - "Work"
