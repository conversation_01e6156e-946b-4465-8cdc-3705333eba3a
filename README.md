# 邮件处理机器人 (Email Processing Bot)

一个智能的本地邮件处理机器人，支持自动读取邮件、AI内容分析和自动化处理。

## 功能特性

- 🔄 **定时邮件读取**: 支持IMAP/POP3协议自动连接邮箱
- 🤖 **智能内容分析**: 集成大语言模型API分析邮件内容
- ⚡ **自动化处理**: 根据分析结果执行相应操作
- 🔧 **模块化设计**: 便于功能扩展和维护
- 🔌 **插件化系统**: 支持自定义处理器插件
- 📊 **日志监控**: 完整的日志记录和错误处理
- ⏰ **定时调度**: 灵活的任务调度机制

## 快速开始

### 环境要求
- Python 3.8+
- pip 或 poetry

### 安装依赖

**方式一：使用启动脚本（推荐）**
```bash
# Linux/macOS
./run.sh install

# Windows
run.bat install
```

**方式二：手动安装**
```bash
# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
# Linux/macOS:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 配置设置

1. **复制配置模板**
   ```bash
   cp config/config.example.yaml config/config.yaml
   ```

2. **编辑配置文件**

   打开 `config/config.yaml` 并填入您的信息：

   ```yaml
   # 邮箱配置
   email:
     host: "imap.gmail.com"          # 您的邮箱服务器
     username: "<EMAIL>" # 您的邮箱
     password: "your-app-password"    # 应用专用密码

   # AI配置
   ai:
     provider: "openai"              # 或 "anthropic"
     api_key: "your-api-key"         # 您的API密钥
     model: "gpt-3.5-turbo"          # 模型名称
   ```

3. **运行机器人**
   ```bash
   # 使用启动脚本
   ./run.sh start

   # 或直接运行
   python main.py
   ```

## 项目结构

```
mailer/
├── src/                    # 源代码目录
│   ├── core/              # 核心模块
│   ├── processors/        # 处理器插件
│   ├── utils/             # 工具函数
│   └── __init__.py
├── config/                # 配置文件
├── logs/                  # 日志文件
├── tests/                 # 测试文件
├── requirements.txt       # 依赖列表
├── main.py               # 主程序入口
├── run.sh                # Linux/macOS启动脚本
└── run.bat               # Windows启动脚本
```

## 使用指南

### 命令行选项

```bash
# 启动机器人（持续运行）
python main.py

# 运行一次处理（不启动调度器）
python main.py --once

# 测试邮箱连接
python main.py --test

# 显示状态信息
python main.py --status

# 使用自定义配置文件
python main.py --config /path/to/config.yaml
```

### 启动脚本使用

```bash
# Linux/macOS
./run.sh start      # 启动机器人
./run.sh once       # 运行一次
./run.sh test       # 测试连接
./run.sh status     # 显示状态
./run.sh install    # 安装依赖
./run.sh help       # 显示帮助

# Windows
run.bat start       # 启动机器人
run.bat once        # 运行一次
run.bat test        # 测试连接
run.bat status      # 显示状态
run.bat install     # 安装依赖
run.bat help        # 显示帮助
```

### 邮箱配置说明

#### Gmail配置示例
```yaml
email:
  host: "imap.gmail.com"
  port: 993
  username: "<EMAIL>"
  password: "your-app-password"  # 需要生成应用专用密码
  use_ssl: true
  protocol: "imap"
```

#### Outlook配置示例
```yaml
email:
  host: "outlook.office365.com"
  port: 993
  username: "<EMAIL>"
  password: "your-password"
  use_ssl: true
  protocol: "imap"
```

#### 企业邮箱配置示例
```yaml
email:
  host: "mail.company.com"
  port: 993
  username: "<EMAIL>"
  password: "your-password"
  use_ssl: true
  protocol: "imap"
```

### AI配置说明

#### OpenAI配置
```yaml
ai:
  provider: "openai"
  api_key: "sk-..."
  model: "gpt-3.5-turbo"  # 或 "gpt-4"
  max_tokens: 1000
  temperature: 0.7
```

#### Anthropic配置
```yaml
ai:
  provider: "anthropic"
  api_key: "sk-ant-..."
  model: "claude-3-sonnet-20240229"
  max_tokens: 1000
  temperature: 0.7
```

## 自定义处理器

### 创建自定义处理器

1. 在 `src/processors/` 目录下创建新的Python文件
2. 继承 `BaseProcessor` 类
3. 实现必要的方法

示例处理器：

```python
from src.core.processor_manager import BaseProcessor
from src.core.ai_analyzer import AnalysisResult

class MyCustomProcessor(BaseProcessor):
    def can_process(self, email_data, analysis):
        # 判断是否处理该邮件
        return analysis.category == "工作"

    def process(self, email_data, analysis):
        # 处理邮件的逻辑
        return {"success": True, "message": "处理完成"}
```

### 内置处理器

- **LogProcessor**: 记录所有邮件处理日志
- **NotificationProcessor**: 发送重要邮件通知
- **AutoReplyProcessor**: 自动回复邮件
- **ExampleProcessor**: 示例处理器，展示如何创建自定义处理器
- **PdfProcessor**: 处理PDF附件
  - 自动下载PDF附件
  - 自动提取文本
  - 自动分析文本
  - 自动发送回复邮件
  - 使用docling库解析PDF 参考 [docling](https://github.com/ankushshah89/docling)


## 故障排除

### 常见问题

**1. 邮箱连接失败**
- 检查邮箱服务器地址和端口
- 确认用户名和密码正确
- 检查是否启用了IMAP/POP3
- 确认防火墙设置

**2. AI分析失败**
- 检查API密钥是否正确
- 确认网络连接正常
- 检查API配额是否用完
- 验证模型名称是否正确

**3. 处理器错误**
- 查看日志文件了解详细错误信息
- 检查处理器配置是否正确
- 确认处理器依赖是否安装

### 日志文件
- 主日志: `logs/mailer.log`
- 错误日志: `logs/mailer_error.log`
- 处理日志: `logs/email_processing.log`

## 开发指南

### 运行测试
```bash
# 安装测试依赖
pip install pytest pytest-asyncio

# 运行测试
pytest tests/

# 运行特定测试
pytest tests/test_email_client.py
```

### 代码格式化
```bash
# 安装格式化工具
pip install black flake8

# 格式化代码
black src/

# 检查代码风格
flake8 src/
```
