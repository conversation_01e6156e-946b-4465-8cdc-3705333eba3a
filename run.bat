@echo off
REM 邮件处理机器人启动脚本 (Windows)

setlocal enabledelayedexpansion

REM 颜色定义 (Windows 10+)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 打印带颜色的消息
:print_message
echo %~2%~1%NC%
goto :eof

REM 检查Python版本
:check_python
python --version >nul 2>&1
if errorlevel 1 (
    call :print_message "错误: 未找到 python" %RED%
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
call :print_message "Python版本: !python_version!" %BLUE%
goto :eof

REM 检查并创建虚拟环境
:setup_venv
if not exist "venv" (
    call :print_message "创建虚拟环境..." %YELLOW%
    python -m venv venv
)

call :print_message "激活虚拟环境..." %BLUE%
call venv\Scripts\activate.bat

REM 升级pip
python -m pip install --upgrade pip
goto :eof

REM 安装依赖
:install_dependencies
call :print_message "安装依赖包..." %YELLOW%
pip install -r requirements.txt
goto :eof

REM 检查配置文件
:check_config
if not exist "config\config.yaml" (
    if exist "config\config.example.yaml" (
        call :print_message "配置文件不存在，复制示例配置..." %YELLOW%
        copy "config\config.example.yaml" "config\config.yaml"
        call :print_message "请编辑 config\config.yaml 文件，填入您的配置信息" %RED%
        exit /b 1
    ) else (
        call :print_message "错误: 配置文件和示例文件都不存在" %RED%
        exit /b 1
    )
)
goto :eof

REM 创建必要的目录
:create_directories
if not exist "logs" mkdir logs
if not exist "config" mkdir config
goto :eof

REM 显示帮助信息
:show_help
echo 邮件处理机器人启动脚本
echo.
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   start     启动机器人 (默认)
echo   once      运行一次处理
echo   test      测试邮箱连接
echo   status    显示状态信息
echo   install   只安装依赖
echo   help      显示此帮助信息
echo.
goto :eof

REM 主函数
:main
set command=%1
if "%command%"=="" set command=start

if "%command%"=="help" (
    call :show_help
    exit /b 0
)

if "%command%"=="install" (
    call :print_message "开始安装..." %GREEN%
    call :check_python
    call :setup_venv
    call :install_dependencies
    call :create_directories
    call :print_message "安装完成!" %GREEN%
    exit /b 0
)

if "%command%"=="start" goto :run_command
if "%command%"=="once" goto :run_command
if "%command%"=="test" goto :run_command
if "%command%"=="status" goto :run_command

call :print_message "未知命令: %command%" %RED%
call :show_help
exit /b 1

:run_command
call :print_message "邮件处理机器人 - %command%" %GREEN%

REM 检查环境
call :check_python
call :create_directories

REM 如果虚拟环境不存在，创建并安装依赖
if not exist "venv" (
    call :setup_venv
    call :install_dependencies
) else (
    call venv\Scripts\activate.bat
)

REM 检查配置
call :check_config

REM 运行程序
if "%command%"=="start" (
    call :print_message "启动邮件处理机器人..." %BLUE%
    python main.py
) else if "%command%"=="once" (
    call :print_message "执行一次性处理..." %BLUE%
    python main.py --once
) else if "%command%"=="test" (
    call :print_message "测试邮箱连接..." %BLUE%
    python main.py --test
) else if "%command%"=="status" (
    call :print_message "显示状态信息..." %BLUE%
    python main.py --status
)

goto :eof

REM 运行主函数
call :main %*
