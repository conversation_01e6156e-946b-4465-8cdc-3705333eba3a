"""
AI分析器测试
"""

import pytest
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.core.config_manager import AIConfig
from src.core.ai_analyzer import AIAnalyzer, AnalysisResult


class TestAIAnalyzer:
    """AI分析器测试类"""

    def test_analysis_result_creation(self):
        """测试分析结果创建"""
        result = AnalysisResult(
            category="工作",
            priority="高",
            sentiment="中性",
            summary="测试邮件",
            action_required=True,
            suggested_actions=["回复邮件"],
            confidence=0.8
        )

        assert result.category == "工作"
        assert result.priority == "高"
        assert result.action_required is True
        assert result.confidence == 0.8

        # 测试转换为字典
        result_dict = result.to_dict()
        assert isinstance(result_dict, dict)
        assert result_dict["category"] == "工作"

    def test_ai_config_validation(self):
        """测试AI配置验证"""
        config = AIConfig(
            provider="openai",
            api_key="test-key",
            model="gpt-3.5-turbo"
        )

        assert config.provider == "openai"
        assert config.model == "gpt-3.5-turbo"
        assert config.max_tokens == 1000  # 默认值

    def test_ai_analyzer_initialization(self):
        """测试AI分析器初始化"""
        config = AIConfig(
            provider="openai",
            api_key="test-key",
            model="gpt-3.5-turbo"
        )

        # 由于没有真实的API密钥，这里只测试初始化
        # 在实际测试中，您需要使用mock或测试API密钥
        try:
            analyzer = AIAnalyzer(config)
            assert analyzer.config == config
        except ValueError as e:
            # 如果提供商不支持，应该抛出ValueError
            assert "不支持的AI提供商" in str(e)


if __name__ == "__main__":
    pytest.main([__file__])
