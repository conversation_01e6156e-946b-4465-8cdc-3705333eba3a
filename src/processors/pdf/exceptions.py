"""
PDF处理异常类定义

定义了PDF处理过程中可能出现的各种异常类型，
提供详细的错误分类和处理机制。
"""


class PDFProcessingError(Exception):
    """PDF处理基础异常类"""
    pass


class PDFFileTooLargeError(PDFProcessingError):
    """PDF文件过大异常"""
    
    def __init__(self, size: int, max_size: int):
        self.size = size
        self.max_size = max_size
        super().__init__(f"PDF文件过大: {size} bytes > {max_size} bytes")


class PDFCorruptedError(PDFProcessingError):
    """PDF文件损坏异常"""
    pass


class PDFEncryptedError(PDFProcessingError):
    """PDF文件加密异常"""
    pass


class PDFTextExtractionError(PDFProcessingError):
    """PDF文本提取异常"""
    pass


class PDFMemoryError(PDFProcessingError):
    """PDF处理内存不足异常"""
    pass
