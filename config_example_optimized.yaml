# 优化后的邮件处理机器人配置示例
# 展示如何配置AI分析器和处理器建议功能

# 邮件服务器配置
email:
  protocol: "imap"  # 或 "pop3"
  server: "imap.gmail.com"
  port: 993
  username: "<EMAIL>"
  password: "your-app-password"
  use_ssl: true
  
  # 邮件检查设置
  check_interval: 60  # 秒
  max_emails_per_check: 10
  mark_as_read: true

# AI分析器配置
ai:
  provider: "openai"  # 或 "anthropic"
  api_key: "your-api-key"
  base_url: "https://api.openai.com/v1"  # 可选，用于自定义API端点
  model: "gpt-3.5-turbo"
  max_tokens: 1000
  temperature: 0.3
  
  # 启用AI处理器建议功能
  enable_processor_suggestions: true
  
  # AI分析超时设置
  timeout: 30

# 处理器配置
processors:
  # 日志处理器（必需，所有邮件都会使用）
  log_processor:
    enabled: true
    log_file: "logs/email_processing.log"
    
  # 通知处理器
  notification_processor:
    enabled: true
    priority_threshold: "高"  # 只处理高优先级邮件
    method: "system"  # system, console
    
  # 自动回复处理器
  auto_reply_processor:
    enabled: true
    reply_template: "感谢您的邮件，我已收到并会尽快处理。"
    exclude_keywords: ["re:", "fwd:", "auto-reply", "out of office"]
    
  # PDF处理器
  pdf_processor:
    enabled: true
    download_path: "temp/pdf_attachments"
    auto_reply: true
    
  # AI回复处理器
  ai_reply_processor:
    enabled: true
    ai_prompt: "请根据以下邮件内容生成专业、友好的回复："
    max_reply_length: 500

# 调度器配置
scheduler:
  enabled: true
  
  # 定时任务
  jobs:
    - name: "check_emails"
      func: "check_and_process_emails"
      trigger: "interval"
      seconds: 60
      
    - name: "cleanup_temp_files"
      func: "cleanup_temp_files"
      trigger: "cron"
      hour: 2
      minute: 0

# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"
  rotation: "1 day"
  retention: "7 days"
  
  # 日志文件
  files:
    - "logs/mailer.log"
    - "logs/email_processing.log"
    - "logs/ai_analysis.log"

# SMTP发送配置（用于回复邮件）
smtp:
  server: "smtp.gmail.com"
  port: 587
  username: "<EMAIL>"
  password: "your-app-password"
  use_tls: true
  from_name: "邮件处理机器人"

# AI处理器建议示例配置
# 这些是AI可能返回的处理器建议格式示例
ai_processor_suggestions_examples:
  # 工作邮件示例
  work_email:
    category: "工作"
    priority: "高"
    processor_suggestions:
      - processor_name: "notification_processor"
        reason: "高优先级工作邮件需要立即通知"
        priority: 1
        enabled: true
      - processor_name: "ai_reply_processor"
        reason: "需要生成专业的工作回复"
        priority: 2
        enabled: true
      - processor_name: "log_processor"
        reason: "记录重要工作邮件"
        priority: 999
        enabled: true
        
  # PDF附件邮件示例
  pdf_email:
    category: "技术支持"
    priority: "中"
    processor_suggestions:
      - processor_name: "pdf_processor"
        reason: "处理PDF附件并提取内容"
        priority: 1
        enabled: true
      - processor_name: "ai_reply_processor"
        reason: "生成PDF处理结果回复"
        priority: 2
        enabled: true
      - processor_name: "log_processor"
        reason: "记录PDF处理日志"
        priority: 999
        enabled: true
        
  # 垃圾邮件示例
  spam_email:
    category: "垃圾邮件"
    priority: "低"
    processor_suggestions:
      - processor_name: "log_processor"
        reason: "仅记录垃圾邮件，不做其他处理"
        priority: 999
        enabled: true
        
  # 会议邀请示例
  meeting_email:
    category: "会议邀请"
    priority: "中"
    processor_suggestions:
      - processor_name: "notification_processor"
        reason: "会议邀请需要及时通知"
        priority: 1
        enabled: true
      - processor_name: "auto_reply_processor"
        reason: "快速确认收到会议邀请"
        priority: 2
        enabled: true
      - processor_name: "log_processor"
        reason: "记录会议邀请处理"
        priority: 999
        enabled: true

# 性能监控配置
monitoring:
  enabled: true
  
  # 统计信息
  stats:
    track_processing_time: true
    track_ai_accuracy: true
    track_processor_success_rate: true
    
  # 报告设置
  reports:
    daily_summary: true
    weekly_analysis: true
    
# 安全配置
security:
  # API密钥加密
  encrypt_api_keys: true
  
  # 邮件内容过滤
  content_filtering:
    enabled: true
    max_content_length: 10000
    blocked_keywords: ["password", "credit card", "ssn"]
    
  # 附件安全
  attachment_security:
    enabled: true
    max_file_size: "10MB"
    allowed_extensions: [".pdf", ".txt", ".doc", ".docx"]
    scan_for_malware: false  # 需要额外的安全工具
