import os
import unittest
from unittest.mock import patch, MagicMock
from src.processors.pdf_processor import PdfProcessor

class TestPdfProcessor(unittest.TestCase):
    def setUp(self):
        self.processor = PdfProcessor()
        self.email_data = {
            "from": "<EMAIL>",
            "subject": "Test PDF",
            "attachments": [
                {
                    "filename": "test.pdf",
                    "content_type": "application/pdf",
                    "download_url": "http://example.com/test.pdf"
                }
            ]
        }
        self.analysis = MagicMock()

    @patch('src.processors.pdf_processor.requests.get')
    @patch('src.processors.pdf_processor.DocumentConverter')
    def test_process_pdf(self, mock_converter, mock_get):
        # 模拟下载响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = b"PDF content"
        mock_get.return_value = mock_response

        # 模拟PDF解析
        mock_doc = MagicMock()
        mock_doc.export_to_text.return_value = "PDF text content"
        mock_result = MagicMock()
        mock_result.document = mock_doc
        mock_converter.return_value.convert.return_value = mock_result

        # 执行处理
        result = self.processor.process(self.email_data, self.analysis)
        
        # 验证结果
        self.assertEqual(len(result["pdf_processing_results"]), 1)
        self.assertTrue(result["pdf_processing_results"][0]["reply_sent"])
        self.assertEqual(result["pdf_processing_results"][0]["filename"], "test.pdf")
        
        # 验证分析结果
        analysis_result = result["pdf_processing_results"][0]["analysis"]
        self.assertIn("page_count", analysis_result)
        self.assertIn("word_count", analysis_result)
        self.assertIn("keywords", analysis_result)

    @patch('src.processors.pdf_processor.requests.get')
    @patch('src.processors.pdf_processor.DocumentConverter')
    def test_download_failure(self, mock_converter, mock_get):
        # 模拟下载失败
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response

        # 执行处理
        result = self.processor.process(self.email_data, self.analysis)
        
        # 验证结果
        self.assertEqual(len(result["pdf_processing_results"]), 0)
        # 下载失败时不会调用PDF解析

    def test_no_pdf_attachment(self):
        # 没有PDF附件的情况
        email_data = self.email_data.copy()
        email_data["attachments"] = [{"filename": "test.txt", "content_type": "text/plain"}]
        
        # 执行处理
        result = self.processor.process(email_data, self.analysis)
        
        # 验证结果
        self.assertEqual(len(result["pdf_processing_results"]), 0)

if __name__ == '__main__':
    unittest.main()