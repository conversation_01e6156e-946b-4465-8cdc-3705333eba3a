# PDF处理器实现计划

## 1. 功能需求
- [x] 自动下载PDF附件
- [x] 提取PDF文本内容
- [x] 分析提取的文本
- [x] 根据分析结果发送回复邮件
- [x] 使用docling库解析PDF

## 2. 架构设计
```mermaid
graph TD
    A[邮件客户端] --> B[获取带PDF附件的邮件]
    B --> C[下载PDF附件]
    C --> D[使用docling解析PDF]
    D --> E[提取文本内容]
    E --> F[AI分析器分析文本]
    F --> G[生成回复内容]
    G --> H[发送回复邮件]
```

## 3. 具体实现步骤

### 步骤1: 扩展邮件附件处理
**文件**: `src/core/email_client.py`
```python
def _extract_attachments(self) -> List[Dict[str, Any]]:
    attachments = []
    if self.raw_message.is_multipart():
        for part in self.raw_message.walk():
            if part.get_content_disposition() == "attachment":
                filename = part.get_filename()
                if filename:
                    # 添加附件内容下载
                    payload = part.get_payload(decode=True)
                    attachments.append({
                        "filename": filename,
                        "content_type": part.get_content_type(),
                        "size": len(payload),
                        "content": payload  # 新增内容字段
                    })
    return attachments
```

### 步骤2: 添加docling依赖
**文件**: `requirements.txt`
```text
docling>=0.1.0  # PDF解析库
```

### 步骤3: 创建PDF处理器
**文件**: `src/processors/pdf_processor.py`
```python
"""
PDF处理器 - 处理PDF附件并自动回复
"""

import os
import tempfile
from typing import Dict, Any
from loguru import logger
from docling import PdfParser  # PDF解析库
from ..core.processor_manager import BaseProcessor
from ..core.ai_analyzer import AnalysisResult, AIAnalyzer

class PdfProcessor(BaseProcessor):
    # 完整实现见计划文档...
```

### 步骤4: 扩展AI分析器
**文件**: `src/core/ai_analyzer.py`
```python
def analyze_text(self, text: str) -> AnalysisResult:
    """分析任意文本内容"""
    # 复用现有邮件分析逻辑，将文本作为输入
    return self.analyze({"body": text})
```

### 步骤5: 注册PDF处理器
**文件**: `src/core/processor_manager.py`
```python
from ..processors.pdf_processor import PdfProcessor
self.register_processor(PdfProcessor("pdf_processor"))
```

## 4. 配置调整
**文件**: `config/config.yaml`
```yaml
processors:
  pdf_processor:
    enabled: true
    # 可添加自定义配置项
```

## 5. 测试计划
1. 单元测试：测试PDF解析和文本提取功能
2. 集成测试：模拟带PDF附件的邮件处理流程
3. 端到端测试：实际发送含PDF附件的邮件验证全流程

## 6. 风险与缓解
| 风险 | 缓解措施 |
|------|----------|
| PDF解析失败 | 添加异常处理和日志记录 |
| 大文件处理 | 添加文件大小限制和流式处理 |
| 文本分析误差 | 提供分析置信度指标 |